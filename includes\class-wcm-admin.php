<?php
/**
 * Admin functionality for Woo Cash Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WCM_Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('admin_init', array($this, 'handle_form_submissions'));
        add_action('admin_notices', array($this, 'activation_notice'));
    }
    
    /**
     * Add admin menu items
     */
    public function add_admin_menu() {
        // Check if WooCommerce menu exists
        if (!current_user_can('manage_woocommerce')) {
            return;
        }

        // Add submenu under WooCommerce
        add_submenu_page(
            'woocommerce',
            __('Cash Manager', 'woo-cash-manager'),
            __('Cash Manager', 'woo-cash-manager'),
            'manage_woocommerce',
            'woo-cash-manager',
            array($this, 'dashboard_page')
        );

        // Add POS submenu
        add_submenu_page(
            'woocommerce',
            __('POS System', 'woo-cash-manager'),
            __('POS System', 'woo-cash-manager'),
            'manage_woocommerce',
            'woo-cash-manager-pos',
            array($this, 'pos_page')
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on our plugin pages
        if (strpos($hook, 'woo-cash') === false) {
            return;
        }

        // Enqueue WordPress admin styles
        wp_enqueue_style('wcm-admin-style', WCM_PLUGIN_URL . 'assets/css/admin.css', array(), WCM_PLUGIN_VERSION);

        // Check if we're on the POS page
        $is_pos_page = (strpos($hook, 'woo-cash-manager-pos') !== false);

        if ($is_pos_page) {
            // POS-specific styles and scripts
            wp_enqueue_style('wcm-pos-style', WCM_PLUGIN_URL . 'assets/css/pos.css', array('wcm-admin-style'), WCM_PLUGIN_VERSION);
            wp_enqueue_script('wcm-pos-script', WCM_PLUGIN_URL . 'assets/js/pos.js', array('jquery'), WCM_PLUGIN_VERSION, true);

            // Localize POS script
            wp_localize_script('wcm-pos-script', 'wcm_pos_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('wcm_pos_nonce'),
                'currency_symbol' => get_woocommerce_currency_symbol(),
                'currency_position' => get_option('woocommerce_currency_pos'),
                'price_decimals' => wc_get_price_decimals(),
                'strings' => array(
                    'add_to_cart' => __('Add to Cart', 'woo-cash-manager'),
                    'remove_item' => __('Remove Item', 'woo-cash-manager'),
                    'confirm_clear_cart' => __('Are you sure you want to clear the cart?', 'woo-cash-manager'),
                    'processing_order' => __('Processing order...', 'woo-cash-manager'),
                    'order_completed' => __('Order completed successfully!', 'woo-cash-manager'),
                    'error_occurred' => __('An error occurred. Please try again.', 'woo-cash-manager'),
                )
            ));
        } else {
            // Regular dashboard scripts
            // Enqueue Chart.js
            wp_enqueue_script('chart-js', 'https://cdn.jsdelivr.net/npm/chart.js', array(), '3.9.1', true);

            // Enqueue our admin script
            wp_enqueue_script('wcm-admin-script', WCM_PLUGIN_URL . 'assets/js/admin.js', array('jquery', 'chart-js'), WCM_PLUGIN_VERSION, true);

            // Localize script for AJAX
            wp_localize_script('wcm-admin-script', 'wcm_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('wcm_nonce'),
                'strings' => array(
                    'confirm_delete' => __('Are you sure you want to delete this expense?', 'woo-cash-manager'),
                    'error_occurred' => __('An error occurred. Please try again.', 'woo-cash-manager'),
                )
            ));
        }
    }
    
    /**
     * Show activation notice
     */
    public function activation_notice() {
        if (get_transient('wcm_activation_notice')) {
            ?>
            <div class="notice notice-success is-dismissible">
                <p>
                    <strong><?php _e('Woo Cash Manager activated successfully!', 'woo-cash-manager'); ?></strong>
                    <?php printf(
                        __('Get started by visiting the <a href="%s">Cash Manager Dashboard</a>.', 'woo-cash-manager'),
                        admin_url('admin.php?page=woo-cash-manager')
                    ); ?>
                </p>
            </div>
            <?php
            delete_transient('wcm_activation_notice');
        }
    }

    /**
     * Handle form submissions
     */
    public function handle_form_submissions() {
        // Handle expense form submission
        if (isset($_POST['wcm_add_expense']) && wp_verify_nonce($_POST['wcm_nonce'], 'wcm_add_expense')) {
            $this->handle_add_expense();
        }
        
        // Handle expense update - use same nonce as add expense since it's the same form
        if (isset($_POST['wcm_update_expense']) && wp_verify_nonce($_POST['wcm_nonce'], 'wcm_add_expense')) {
            $this->handle_update_expense();
        }
    }
    
    /**
     * Dashboard page
     */
    public function dashboard_page() {
        $cash_data = $this->get_cash_data();
        $monthly_data = $this->get_monthly_data();

        // Pagination for expenses
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $per_page = 10; // Show 10 expenses per page on dashboard
        $offset = ($current_page - 1) * $per_page;

        // Get expenses and pagination data
        $expenses = $this->get_expenses($per_page, $offset);
        $total_expenses = $this->get_expenses_count();
        $total_pages = ceil($total_expenses / $per_page);

        // Get common categories for the modal form
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        $common_categories = $wpdb->get_col("SELECT DISTINCT category FROM $table_name WHERE category != '' ORDER BY category LIMIT 20");

        include WCM_PLUGIN_PATH . 'templates/dashboard.php';
    }

    /**
     * POS page
     */
    public function pos_page() {
        // Check if user has permission
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'woo-cash-manager'));
        }

        // Initialize POS system if not already done
        if (!class_exists('WCM_POS')) {
            require_once WCM_PLUGIN_PATH . 'includes/class-wcm-pos.php';
        }

        $pos = new WCM_POS();

        // Get available products for POS
        $products = $pos->get_pos_products();

        // Get available payment methods
        $payment_methods = $pos->get_available_payment_methods();

        // Get current cart (if any)
        $cart_items = $pos->get_cart_items();
        $cart_totals = $pos->get_cart_totals();

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('WCM POS Page Load - Cart Items: ' . print_r($cart_items, true));
            error_log('WCM POS Page Load - Cart Totals: ' . print_r($cart_totals, true));
        }

        include WCM_PLUGIN_PATH . 'templates/pos-dashboard.php';
    }
    

    
    /**
     * Get cash data for dashboard
     */
    private function get_cash_data() {
        $income = $this->get_total_income();
        $expenses = $this->get_total_expenses();
        $balance = $income - $expenses;
        
        return array(
            'income' => $income,
            'expenses' => $expenses,
            'balance' => $balance
        );
    }
    
    /**
     * Get total income from WooCommerce orders (HPOS Compatible)
     */
    private function get_total_income() {
        // Use HPOS compatible method
        if (class_exists('\Automattic\WooCommerce\Utilities\OrderUtil') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            // HPOS is enabled, use direct database query for better performance
            global $wpdb;

            $orders_table = $wpdb->prefix . 'wc_orders';
            $total = $wpdb->get_var($wpdb->prepare(
                "SELECT SUM(total_amount) FROM {$orders_table}
                 WHERE status IN ('wc-completed', 'wc-processing')
                 AND type = 'shop_order'"
            ));

            return $total ? floatval($total) : 0;

        } else {
            // Fallback to traditional method for legacy stores
            $orders = wc_get_orders(array(
                'status' => array('completed', 'processing'),
                'limit' => -1,
                'return' => 'ids'
            ));

            $total = 0;
            foreach ($orders as $order_id) {
                $order = wc_get_order($order_id);
                if ($order) {
                    $total += $order->get_total();
                }
            }

            return $total;
        }
    }
    
    /**
     * Get total expenses
     */
    private function get_total_expenses() {
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        
        $total = $wpdb->get_var("SELECT SUM(amount) FROM $table_name");
        
        return $total ? $total : 0;
    }
    
    /**
     * Get monthly data for charts
     */
    private function get_monthly_data() {
        $months = array();
        $income_data = array();
        $expense_data = array();
        
        // Get last 12 months
        for ($i = 11; $i >= 0; $i--) {
            $date = date('Y-m', strtotime("-$i months"));
            $months[] = date('M Y', strtotime("-$i months"));
            
            // Get income for this month
            $income_data[] = $this->get_monthly_income($date);
            
            // Get expenses for this month
            $expense_data[] = $this->get_monthly_expenses($date);
        }
        
        return array(
            'months' => $months,
            'income' => $income_data,
            'expenses' => $expense_data
        );
    }
    
    /**
     * Get monthly income (HPOS Compatible)
     */
    private function get_monthly_income($month) {
        $start_date = $month . '-01';
        $end_date = date('Y-m-t', strtotime($start_date));

        // Use HPOS compatible method
        if (class_exists('\Automattic\WooCommerce\Utilities\OrderUtil') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            // HPOS is enabled
            global $wpdb;

            $orders_table = $wpdb->prefix . 'wc_orders';
            $total = $wpdb->get_var($wpdb->prepare(
                "SELECT SUM(total_amount) FROM {$orders_table}
                 WHERE status IN ('wc-completed', 'wc-processing')
                 AND type = 'shop_order'
                 AND date_created_gmt BETWEEN %s AND %s",
                $start_date . ' 00:00:00',
                $end_date . ' 23:59:59'
            ));

            return $total ? floatval($total) : 0;

        } else {
            // Fallback to traditional method
            $orders = wc_get_orders(array(
                'status' => array('completed', 'processing'),
                'date_created' => $start_date . '...' . $end_date,
                'limit' => -1,
                'return' => 'ids'
            ));

            $total = 0;
            foreach ($orders as $order_id) {
                $order = wc_get_order($order_id);
                if ($order) {
                    $total += $order->get_total();
                }
            }

            return $total;
        }
    }
    
    /**
     * Get monthly expenses
     */
    private function get_monthly_expenses($month) {
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        
        $start_date = $month . '-01';
        $end_date = date('Y-m-t', strtotime($start_date));
        
        $total = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(amount) FROM $table_name WHERE expense_date BETWEEN %s AND %s",
            $start_date,
            $end_date
        ));
        
        return $total ? $total : 0;
    }
    
    /**
     * Handle add expense
     */
    private function handle_add_expense() {
        $title = sanitize_text_field($_POST['title']);
        $amount = floatval($_POST['amount']);
        $category = sanitize_text_field($_POST['category']);
        $note = sanitize_textarea_field($_POST['note']);
        $expense_date = sanitize_text_field($_POST['expense_date']);
        
        if (empty($title) || $amount <= 0 || empty($category) || empty($expense_date)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Please fill in all required fields.', 'woo-cash-manager') . '</p></div>';
            });
            return;
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        
        $result = $wpdb->insert(
            $table_name,
            array(
                'title' => $title,
                'amount' => $amount,
                'category' => $category,
                'note' => $note,
                'expense_date' => $expense_date
            ),
            array('%s', '%f', '%s', '%s', '%s')
        );
        
        if ($result) {
            wp_redirect(admin_url('admin.php?page=woo-cash-manager&message=added'));
            exit;
        } else {
            // Log the error for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('WCM: Failed to insert expense. DB Error: ' . $wpdb->last_error);
            }

            add_action('admin_notices', function() use ($wpdb) {
                $error_msg = __('Failed to add expense.', 'woo-cash-manager');
                if (defined('WP_DEBUG') && WP_DEBUG && $wpdb->last_error) {
                    $error_msg .= ' Debug: ' . $wpdb->last_error;
                }
                echo '<div class="notice notice-error"><p>' . $error_msg . '</p></div>';
            });
        }
    }
    
    /**
     * Handle update expense
     */
    private function handle_update_expense() {
        $expense_id = intval($_POST['expense_id']);
        $title = sanitize_text_field($_POST['title']);
        $amount = floatval($_POST['amount']);
        $category = sanitize_text_field($_POST['category']);
        $note = sanitize_textarea_field($_POST['note']);
        $expense_date = sanitize_text_field($_POST['expense_date']);

        if (empty($title) || $amount <= 0 || empty($category) || empty($expense_date)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Please fill in all required fields.', 'woo-cash-manager') . '</p></div>';
            });
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;

        $result = $wpdb->update(
            $table_name,
            array(
                'title' => $title,
                'amount' => $amount,
                'category' => $category,
                'note' => $note,
                'expense_date' => $expense_date
            ),
            array('id' => $expense_id),
            array('%s', '%f', '%s', '%s', '%s'),
            array('%d')
        );

        if ($result !== false) {
            wp_redirect(admin_url('admin.php?page=woo-cash-manager&message=updated'));
            exit;
        } else {
            // Log the error for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('WCM: Failed to update expense. DB Error: ' . $wpdb->last_error);
            }

            add_action('admin_notices', function() use ($wpdb) {
                $error_msg = __('Failed to update expense.', 'woo-cash-manager');
                if (defined('WP_DEBUG') && WP_DEBUG && $wpdb->last_error) {
                    $error_msg .= ' Debug: ' . $wpdb->last_error;
                }
                echo '<div class="notice notice-error"><p>' . $error_msg . '</p></div>';
            });
        }
    }
    
    /**
     * Get all expenses with filtering
     */
    private function get_expenses($limit = 20, $offset = 0) {
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;

        $where_clauses = array();
        $where_values = array();

        // Search filter
        if (isset($_GET['search']) && !empty($_GET['search'])) {
            $search = sanitize_text_field($_GET['search']);
            $where_clauses[] = "(title LIKE %s OR note LIKE %s OR category LIKE %s)";
            $where_values[] = '%' . $wpdb->esc_like($search) . '%';
            $where_values[] = '%' . $wpdb->esc_like($search) . '%';
            $where_values[] = '%' . $wpdb->esc_like($search) . '%';
        }

        // Category filter
        if (isset($_GET['category']) && !empty($_GET['category'])) {
            $category = sanitize_text_field($_GET['category']);
            $where_clauses[] = "category = %s";
            $where_values[] = $category;
        }

        // Date range filter
        if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
            $date_from = sanitize_text_field($_GET['date_from']);
            $where_clauses[] = "expense_date >= %s";
            $where_values[] = $date_from;
        }

        if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
            $date_to = sanitize_text_field($_GET['date_to']);
            $where_clauses[] = "expense_date <= %s";
            $where_values[] = $date_to;
        }

        // Category filter
        if (isset($_GET['category']) && !empty($_GET['category'])) {
            $category = sanitize_text_field($_GET['category']);
            $where_clauses[] = "category = %s";
            $where_values[] = $category;
        }

        // Date range filter
        if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
            $date_from = sanitize_text_field($_GET['date_from']);
            $where_clauses[] = "expense_date >= %s";
            $where_values[] = $date_from;
        }

        if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
            $date_to = sanitize_text_field($_GET['date_to']);
            $where_clauses[] = "expense_date <= %s";
            $where_values[] = $date_to;
        }

        // Build query
        $query = "SELECT * FROM $table_name";
        if (!empty($where_clauses)) {
            $query .= " WHERE " . implode(' AND ', $where_clauses);
        }

        // Sorting
        $sort_by = isset($_GET['sort']) ? sanitize_text_field($_GET['sort']) : 'expense_date';
        $sort_order = isset($_GET['order']) && $_GET['order'] === 'asc' ? 'ASC' : 'DESC';

        // Validate sort column
        $allowed_sort_columns = array('title', 'amount', 'category', 'expense_date', 'created_at');
        if (!in_array($sort_by, $allowed_sort_columns)) {
            $sort_by = 'expense_date';
        }

        $query .= " ORDER BY $sort_by $sort_order, id DESC LIMIT %d OFFSET %d";

        // Add limit and offset to values
        $where_values[] = $limit;
        $where_values[] = $offset;

        if (!empty($where_values)) {
            $expenses = $wpdb->get_results($wpdb->prepare($query, $where_values));
        } else {
            $expenses = $wpdb->get_results($wpdb->prepare($query, $limit, $offset));
        }

        return $expenses;
    }
    
    /**
     * Get single expense
     */
    private function get_expense($expense_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;

        $expense = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $expense_id
        ));

        return $expense;
    }

    /**
     * Get total count of expenses with filters
     */
    private function get_expenses_count() {
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;

        $where_clauses = array();
        $where_values = array();

        // Search filter
        if (isset($_GET['search']) && !empty($_GET['search'])) {
            $search = sanitize_text_field($_GET['search']);
            $where_clauses[] = "(title LIKE %s OR note LIKE %s OR category LIKE %s)";
            $where_values[] = '%' . $wpdb->esc_like($search) . '%';
            $where_values[] = '%' . $wpdb->esc_like($search) . '%';
            $where_values[] = '%' . $wpdb->esc_like($search) . '%';
        }

        // Category filter
        if (isset($_GET['category']) && !empty($_GET['category'])) {
            $category = sanitize_text_field($_GET['category']);
            $where_clauses[] = "category = %s";
            $where_values[] = $category;
        }

        // Date range filter
        if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
            $date_from = sanitize_text_field($_GET['date_from']);
            $where_clauses[] = "expense_date >= %s";
            $where_values[] = $date_from;
        }

        if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
            $date_to = sanitize_text_field($_GET['date_to']);
            $where_clauses[] = "expense_date <= %s";
            $where_values[] = $date_to;
        }

        // Build query
        $query = "SELECT COUNT(*) FROM $table_name";
        if (!empty($where_clauses)) {
            $query .= " WHERE " . implode(' AND ', $where_clauses);
        }

        if (!empty($where_values)) {
            $count = $wpdb->get_var($wpdb->prepare($query, $where_values));
        } else {
            $count = $wpdb->get_var($query);
        }

        return $count ? $count : 0;
    }
}
