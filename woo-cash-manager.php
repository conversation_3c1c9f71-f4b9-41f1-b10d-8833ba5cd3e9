<?php
/**
 * Plugin Name: Woo Cash Manager
 * Plugin URI: https://example.com/woo-cash-manager
 * Description: A comprehensive cash management plugin for WooCommerce that tracks income from orders and manually added expenses to calculate current cash balance.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: woo-cash-manager
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 * Woo: 8.0.0:tested
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WCM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WCM_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('WCM_PLUGIN_VERSION', '1.0.0');
define('WCM_TABLE_NAME', 'wc_cash_expenses');

/**
 * Main Woo Cash Manager Class
 */
class WooCashManager {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'check_woocommerce'));

        // Declare HPOS compatibility early
        add_action('before_woocommerce_init', array($this, 'declare_hpos_compatibility'));

        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain('woo-cash-manager', false, dirname(plugin_basename(__FILE__)) . '/languages');

        // Initialize admin functionality
        if (is_admin()) {
            $this->init_admin();
        }

        // Initialize AJAX handlers
        $this->init_ajax();
    }
    
    /**
     * Declare HPOS compatibility
     */
    public function declare_hpos_compatibility() {
        if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility(
                'custom_order_tables',
                __FILE__,
                true
            );

            // Also declare compatibility with other WooCommerce features
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility(
                'orders_cache',
                __FILE__,
                true
            );
        }
    }

    /**
     * Check if WooCommerce is active
     */
    public function check_woocommerce() {
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }
    }
    
    /**
     * Display notice if WooCommerce is not active
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php _e('Woo Cash Manager requires WooCommerce to be installed and active.', 'woo-cash-manager'); ?></p>
        </div>
        <?php
    }
    
    /**
     * Initialize admin functionality
     */
    private function init_admin() {
        // Only load admin functionality for users with proper capabilities
        if (current_user_can('manage_woocommerce')) {
            require_once WCM_PLUGIN_PATH . 'includes/class-wcm-admin.php';
            new WCM_Admin();
        }
    }
    
    /**
     * Initialize AJAX handlers
     */
    private function init_ajax() {
        require_once WCM_PLUGIN_PATH . 'includes/class-wcm-ajax.php';
        new WCM_Ajax();

        // Initialize POS AJAX handlers
        require_once WCM_PLUGIN_PATH . 'includes/class-wcm-pos-ajax.php';
        new WCM_POS_Ajax();
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die(__('Woo Cash Manager requires WooCommerce to be installed and active.', 'woo-cash-manager'));
        }

        // Create database table
        $this->create_tables();

        // Set plugin version
        update_option('wcm_plugin_version', WCM_PLUGIN_VERSION);

        // Set activation flag for welcome notice
        set_transient('wcm_activation_notice', true, 30);

        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            amount decimal(10,2) NOT NULL,
            category varchar(100) NOT NULL,
            note text,
            expense_date date NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY expense_date (expense_date),
            KEY category (category)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}

// Initialize the plugin
new WooCashManager();

/**
 * Helper function to get the main plugin instance
 */
function woo_cash_manager() {
    return new WooCashManager();
}

/**
 * Helper function to check if HPOS is enabled
 */
function wcm_is_hpos_enabled() {
    return class_exists('\Automattic\WooCommerce\Utilities\OrderUtil') &&
           \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled();
}

/**
 * Helper function to get WooCommerce orders table name
 */
function wcm_get_orders_table() {
    global $wpdb;

    if (wcm_is_hpos_enabled()) {
        return $wpdb->prefix . 'wc_orders';
    } else {
        return $wpdb->posts;
    }
}
