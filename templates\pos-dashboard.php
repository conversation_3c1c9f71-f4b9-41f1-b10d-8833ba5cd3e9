<?php
/**
 * POS Dashboard Template
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get message from URL parameter
$message = isset($_GET['message']) ? sanitize_text_field($_GET['message']) : '';
?>

<div class="wrap wcm-pos-dashboard">
    <div class="wcm-pos-header">
        <h1 class="wp-heading-inline">
            <span class="dashicons dashicons-store"></span>
            <?php _e('Point of Sale System', 'woo-cash-manager'); ?>
        </h1>
        <a href="<?php echo admin_url('admin.php?page=woo-cash-manager'); ?>" class="button button-secondary">
            <span class="dashicons dashicons-arrow-left-alt"></span>
            <?php _e('Back to Dashboard', 'woo-cash-manager'); ?>
        </a>
    </div>
    <hr class="wp-header-end">
    
    <?php if ($message): ?>
        <div class="notice notice-success is-dismissible">
            <p>
                <?php
                switch ($message) {
                    case 'order_created':
                        _e('Order created successfully!', 'woo-cash-manager');
                        break;
                    case 'payment_processed':
                        _e('Payment processed successfully!', 'woo-cash-manager');
                        break;
                }
                ?>
            </p>
        </div>
    <?php endif; ?>
    
    <div class="wcm-pos-container">
        <!-- Products Section -->
        <div class="wcm-pos-products-section">
            <!-- Search and Filters -->
            <div class="wcm-pos-filters">
                <div class="wcm-pos-search-container">
                    <input type="text" id="wcm-pos-search" placeholder="<?php _e('Search products...', 'woo-cash-manager'); ?>" class="wcm-pos-search-input">
                    <button type="button" id="wcm-pos-search-btn" class="wcm-pos-search-btn">
                        <span class="dashicons dashicons-search"></span>
                    </button>
                </div>
                
                <div class="wcm-pos-filter-container">
                    <select id="wcm-pos-category-filter" class="wcm-pos-filter-select">
                        <option value=""><?php _e('All Categories', 'woo-cash-manager'); ?></option>
                        <?php foreach ($pos->get_all_product_categories() as $category): ?>
                            <option value="<?php echo esc_attr($category['slug']); ?>">
                                <?php echo esc_html($category['name']); ?> (<?php echo $category['count']; ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="wcm-pos-view-toggle">
                    <button type="button" id="wcm-pos-grid-view" class="wcm-pos-view-btn active" data-view="grid">
                        <span class="dashicons dashicons-grid-view"></span>
                    </button>
                    <button type="button" id="wcm-pos-list-view" class="wcm-pos-view-btn" data-view="list">
                        <span class="dashicons dashicons-list-view"></span>
                    </button>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="wcm-pos-products-container" id="wcm-pos-products-grid">
                <div class="wcm-pos-loading" id="wcm-pos-loading">
                    <div class="wcm-spinner"></div>
                    <p><?php _e('Loading products...', 'woo-cash-manager'); ?></p>
                </div>
                
                <div class="wcm-pos-products-grid" id="wcm-pos-products-list">
                    <?php foreach ($products as $product): ?>
                        <div class="wcm-pos-product-card" data-product-id="<?php echo $product['id']; ?>">
                            <div class="wcm-pos-product-image">
                                <?php if ($product['image_url']): ?>
                                    <img src="<?php echo esc_url($product['image_url']); ?>" alt="<?php echo esc_attr($product['name']); ?>">
                                <?php else: ?>
                                    <div class="wcm-pos-no-image">
                                        <span class="dashicons dashicons-format-image"></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($product['sale_price']): ?>
                                    <div class="wcm-pos-sale-badge"><?php _e('Sale', 'woo-cash-manager'); ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="wcm-pos-product-info">
                                <h3 class="wcm-pos-product-name"><?php echo esc_html($product['name']); ?></h3>
                                
                                <?php if ($product['sku']): ?>
                                    <div class="wcm-pos-product-sku">SKU: <?php echo esc_html($product['sku']); ?></div>
                                <?php endif; ?>
                                
                                <div class="wcm-pos-product-price">
                                    <?php if ($product['sale_price']): ?>
                                        <span class="wcm-pos-regular-price"><?php echo wc_price($product['regular_price']); ?></span>
                                        <span class="wcm-pos-sale-price"><?php echo wc_price($product['sale_price']); ?></span>
                                    <?php else: ?>
                                        <span class="wcm-pos-current-price"><?php echo wc_price($product['price']); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="wcm-pos-product-stock">
                                    <?php if ($product['stock_quantity'] !== null): ?>
                                        <span class="wcm-pos-stock-qty"><?php echo sprintf(__('Stock: %d', 'woo-cash-manager'), $product['stock_quantity']); ?></span>
                                    <?php else: ?>
                                        <span class="wcm-pos-stock-status"><?php echo ucfirst($product['stock_status']); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="wcm-pos-product-actions">
                                    <?php if ($product['type'] === 'variable'): ?>
                                        <button type="button" class="wcm-pos-select-variation-btn" data-product-id="<?php echo $product['id']; ?>">
                                            <span class="dashicons dashicons-admin-settings"></span>
                                            <?php _e('Select Options', 'woo-cash-manager'); ?>
                                        </button>
                                    <?php else: ?>
                                        <button type="button" class="wcm-pos-add-to-cart-btn" data-product-id="<?php echo $product['id']; ?>">
                                            <span class="dashicons dashicons-plus-alt"></span>
                                            <?php _e('Add to Cart', 'woo-cash-manager'); ?>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Load More Button -->
                <div class="wcm-pos-load-more-container">
                    <button type="button" id="wcm-pos-load-more" class="wcm-pos-load-more-btn">
                        <?php _e('Load More Products', 'woo-cash-manager'); ?>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Cart Sidebar -->
        <div class="wcm-pos-cart-section">
            <div class="wcm-pos-cart-header">
                <h2>
                    <span class="dashicons dashicons-cart"></span>
                    <?php _e('Shopping Cart', 'woo-cash-manager'); ?>
                </h2>
                <button type="button" id="wcm-pos-clear-cart" class="wcm-pos-clear-cart-btn" title="<?php _e('Clear Cart', 'woo-cash-manager'); ?>">
                    <span class="dashicons dashicons-trash"></span>
                </button>
            </div>
            
            <div class="wcm-pos-cart-items" id="wcm-pos-cart-items">
                <?php if (empty($cart_items)): ?>
                    <div class="wcm-pos-empty-cart">
                        <span class="dashicons dashicons-cart"></span>
                        <p><?php _e('Cart is empty', 'woo-cash-manager'); ?></p>
                        <small><?php _e('Add products to get started', 'woo-cash-manager'); ?></small>
                    </div>
                <?php else: ?>
                    <?php foreach ($cart_items as $cart_item_key => $item): ?>
                        <div class="wcm-pos-cart-item" data-cart-key="<?php echo esc_attr($cart_item_key); ?>">
                            <div class="wcm-pos-cart-item-info">
                                <h4><?php echo esc_html($item['name']); ?></h4>
                                <?php if ($item['sku']): ?>
                                    <small>SKU: <?php echo esc_html($item['sku']); ?></small>
                                <?php endif; ?>
                                <div class="wcm-pos-cart-item-price"><?php echo wc_price($item['price']); ?></div>
                            </div>
                            
                            <div class="wcm-pos-cart-item-controls">
                                <div class="wcm-pos-quantity-controls">
                                    <button type="button" class="wcm-pos-qty-decrease" data-cart-key="<?php echo esc_attr($cart_item_key); ?>">-</button>
                                    <input type="number" class="wcm-pos-qty-input" value="<?php echo $item['quantity']; ?>" min="1" data-cart-key="<?php echo esc_attr($cart_item_key); ?>">
                                    <button type="button" class="wcm-pos-qty-increase" data-cart-key="<?php echo esc_attr($cart_item_key); ?>">+</button>
                                </div>
                                
                                <button type="button" class="wcm-pos-remove-item" data-cart-key="<?php echo esc_attr($cart_item_key); ?>" title="<?php _e('Remove Item', 'woo-cash-manager'); ?>">
                                    <span class="dashicons dashicons-no-alt"></span>
                                </button>
                            </div>
                            
                            <div class="wcm-pos-cart-item-total">
                                <?php echo wc_price($item['price'] * $item['quantity']); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            
            <!-- Cart Totals -->
            <div class="wcm-pos-cart-totals" id="wcm-pos-cart-totals">
                <div class="wcm-pos-subtotal">
                    <span><?php _e('Subtotal:', 'woo-cash-manager'); ?></span>
                    <span id="wcm-pos-subtotal-amount"><?php echo $cart_totals['formatted_subtotal']; ?></span>
                </div>
                
                <?php if ($cart_totals['tax_total'] > 0): ?>
                    <div class="wcm-pos-tax">
                        <span><?php _e('Tax:', 'woo-cash-manager'); ?></span>
                        <span id="wcm-pos-tax-amount"><?php echo $cart_totals['formatted_tax_total']; ?></span>
                    </div>
                <?php endif; ?>
                
                <div class="wcm-pos-total">
                    <span><?php _e('Total:', 'woo-cash-manager'); ?></span>
                    <span id="wcm-pos-total-amount"><?php echo $cart_totals['formatted_total']; ?></span>
                </div>
            </div>
            
            <!-- Payment Section -->
            <div class="wcm-pos-payment-section">
                <h3><?php _e('Payment Method', 'woo-cash-manager'); ?></h3>
                
                <div class="wcm-pos-payment-methods">
                    <?php foreach ($payment_methods as $method): ?>
                        <label class="wcm-pos-payment-method">
                            <input type="radio" name="payment_method" value="<?php echo esc_attr($method['id']); ?>" <?php checked($method['id'], 'cash'); ?>>
                            <span class="wcm-pos-payment-method-content">
                                <span class="dashicons <?php echo esc_attr($method['icon']); ?>"></span>
                                <span class="wcm-pos-payment-method-title"><?php echo esc_html($method['title']); ?></span>
                            </span>
                        </label>
                    <?php endforeach; ?>
                </div>
                
                <!-- Customer Information (Optional) -->
                <div class="wcm-pos-customer-section">
                    <button type="button" id="wcm-pos-toggle-customer" class="wcm-pos-toggle-customer-btn">
                        <span class="dashicons dashicons-admin-users"></span>
                        <?php _e('Add Customer Info', 'woo-cash-manager'); ?>
                    </button>
                    
                    <div class="wcm-pos-customer-form" id="wcm-pos-customer-form" style="display: none;">
                        <input type="text" id="wcm-pos-customer-first-name" placeholder="<?php _e('First Name', 'woo-cash-manager'); ?>">
                        <input type="text" id="wcm-pos-customer-last-name" placeholder="<?php _e('Last Name', 'woo-cash-manager'); ?>">
                        <input type="email" id="wcm-pos-customer-email" placeholder="<?php _e('Email', 'woo-cash-manager'); ?>">
                        <input type="tel" id="wcm-pos-customer-phone" placeholder="<?php _e('Phone', 'woo-cash-manager'); ?>">
                    </div>
                </div>
                
                <!-- Checkout Button -->
                <button type="button" id="wcm-pos-checkout" class="wcm-pos-checkout-btn" <?php echo empty($cart_items) ? 'disabled' : ''; ?>>
                    <span class="dashicons dashicons-yes-alt"></span>
                    <?php _e('Complete Sale', 'woo-cash-manager'); ?>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Product Variation Modal -->
<div id="wcm-pos-variation-modal" class="wcm-modal">
    <div class="wcm-modal-content wcm-pos-variation-modal-content">
        <div class="wcm-modal-header">
            <h3 id="wcm-pos-variation-modal-title"><?php _e('Select Product Options', 'woo-cash-manager'); ?></h3>
            <span class="wcm-modal-close">&times;</span>
        </div>
        <div class="wcm-modal-body" id="wcm-pos-variation-modal-body">
            <!-- Variation content will be loaded here -->
        </div>
        <div class="wcm-modal-footer">
            <button type="button" class="button" id="wcm-pos-variation-cancel"><?php _e('Cancel', 'woo-cash-manager'); ?></button>
            <button type="button" class="button button-primary" id="wcm-pos-variation-add-to-cart"><?php _e('Add to Cart', 'woo-cash-manager'); ?></button>
        </div>
    </div>
</div>

<!-- Receipt Modal -->
<div id="wcm-pos-receipt-modal" class="wcm-modal">
    <div class="wcm-modal-content wcm-pos-receipt-modal-content">
        <div class="wcm-modal-header">
            <h3><?php _e('Order Receipt', 'woo-cash-manager'); ?></h3>
            <span class="wcm-modal-close">&times;</span>
        </div>
        <div class="wcm-modal-body" id="wcm-pos-receipt-content">
            <!-- Receipt content will be loaded here -->
        </div>
        <div class="wcm-modal-footer">
            <button type="button" class="button" id="wcm-pos-print-receipt"><?php _e('Print Receipt', 'woo-cash-manager'); ?></button>
            <button type="button" class="button button-primary" id="wcm-pos-new-sale"><?php _e('New Sale', 'woo-cash-manager'); ?></button>
        </div>
    </div>
</div>

<script type="text/javascript">
// Pass PHP data to JavaScript
window.wcmPosData = {
    products: <?php echo json_encode($products); ?>,
    cartItems: <?php echo json_encode($cart_items); ?>,
    cartTotals: <?php echo json_encode($cart_totals); ?>,
    paymentMethods: <?php echo json_encode($payment_methods); ?>
};
</script>
