/**
 * POS styles for Woo Cash Manager
 */

/* POS Dashboard Layout */
.wcm-pos-dashboard {
    max-width: 100%;
    margin: 0;
    padding: 0 20px;
}

.wcm-pos-dashboard .wp-heading-inline {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.wcm-pos-dashboard .wp-heading-inline .dashicons {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 28px;
}

/* Main POS Container */
.wcm-pos-container {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 20px;
    margin-top: 20px;
    height: calc(100vh - 200px);
    min-height: 600px;
}

/* Products Section */
.wcm-pos-products-section {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 1px solid #e1e5e9;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Filters */
.wcm-pos-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f1;
}

.wcm-pos-search-container {
    display: flex;
    flex: 1;
    min-width: 250px;
    position: relative;
}

.wcm-pos-search-input {
    flex: 1;
    padding: 10px 45px 10px 15px;
    border: 1px solid #ddd;
    border-radius: 25px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fff;
}

.wcm-pos-search-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    outline: none;
}

.wcm-pos-search-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wcm-pos-search-btn:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.wcm-pos-filter-container {
    min-width: 180px;
}

.wcm-pos-filter-select {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    background: #fff;
    transition: all 0.3s ease;
}

.wcm-pos-filter-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    outline: none;
}

.wcm-pos-view-toggle {
    display: flex;
    gap: 5px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 3px;
}

.wcm-pos-view-btn {
    background: transparent;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #646970;
}

.wcm-pos-view-btn.active,
.wcm-pos-view-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    transform: translateY(-1px);
}

/* Products Container */
.wcm-pos-products-container {
    flex: 1;
    overflow-y: auto;
    position: relative;
}

.wcm-pos-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #646970;
}

.wcm-pos-loading .wcm-spinner {
    margin-bottom: 15px;
}

/* Products Grid */
.wcm-pos-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    padding: 10px 0;
}

/* Product Card */
.wcm-pos-product-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 15px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.wcm-pos-product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.wcm-pos-product-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: #667eea;
}

.wcm-pos-product-card:hover::before {
    opacity: 1;
}

.wcm-pos-product-image {
    position: relative;
    width: 100%;
    height: 120px;
    margin-bottom: 12px;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wcm-pos-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.wcm-pos-product-card:hover .wcm-pos-product-image img {
    transform: scale(1.05);
}

.wcm-pos-no-image {
    color: #c3c4c7;
    font-size: 40px;
}

.wcm-pos-sale-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: linear-gradient(135deg, #ff6b6b 0%, #d63638 100%);
    color: #fff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.wcm-pos-product-info {
    text-align: center;
}

.wcm-pos-product-name {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 8px;
    color: #1d2327;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.wcm-pos-product-sku {
    font-size: 11px;
    color: #646970;
    margin-bottom: 8px;
    font-weight: 500;
}

.wcm-pos-product-price {
    margin-bottom: 8px;
}

.wcm-pos-regular-price {
    text-decoration: line-through;
    color: #646970;
    font-size: 12px;
    margin-right: 5px;
}

.wcm-pos-sale-price,
.wcm-pos-current-price {
    font-size: 16px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.wcm-pos-product-stock {
    margin-bottom: 12px;
    font-size: 11px;
    color: #646970;
}

.wcm-pos-stock-qty {
    background: rgba(0, 163, 42, 0.1);
    color: #00a32a;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 600;
}

.wcm-pos-product-actions {
    margin-top: 12px;
}

.wcm-pos-add-to-cart-btn,
.wcm-pos-select-variation-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.wcm-pos-add-to-cart-btn:hover,
.wcm-pos-select-variation-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Load More */
.wcm-pos-load-more-container {
    text-align: center;
    padding: 20px 0;
}

.wcm-pos-load-more-btn {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 1px solid #e1e5e9;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #667eea;
}

.wcm-pos-load-more-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

/* Cart Section */
.wcm-pos-cart-section {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 1px solid #e1e5e9;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.wcm-pos-cart-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.wcm-pos-cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f1;
}

.wcm-pos-cart-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.wcm-pos-clear-cart-btn {
    background: rgba(214, 54, 56, 0.1);
    border: 1px solid rgba(214, 54, 56, 0.2);
    color: #d63638;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.wcm-pos-clear-cart-btn:hover {
    background: #d63638;
    color: #fff;
    transform: scale(1.1);
}

/* Cart Items */
.wcm-pos-cart-items {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 20px;
    max-height: 300px;
}

.wcm-pos-empty-cart {
    text-align: center;
    padding: 40px 20px;
    color: #646970;
}

.wcm-pos-empty-cart .dashicons {
    font-size: 48px;
    color: #c3c4c7;
    margin-bottom: 15px;
}

.wcm-pos-empty-cart p {
    margin: 10px 0 5px;
    font-size: 16px;
    font-weight: 600;
}

.wcm-pos-empty-cart small {
    font-size: 13px;
}

.wcm-pos-cart-item {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #f0f0f1;
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    position: relative;
}

.wcm-pos-cart-item:hover {
    background: rgba(255, 255, 255, 1);
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.wcm-pos-cart-item-info h4 {
    margin: 0 0 5px;
    font-size: 14px;
    font-weight: 600;
    color: #1d2327;
}

.wcm-pos-cart-item-info small {
    color: #646970;
    font-size: 11px;
}

.wcm-pos-cart-item-price {
    font-size: 13px;
    font-weight: 600;
    color: #667eea;
    margin-top: 5px;
}

.wcm-pos-cart-item-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
}

.wcm-pos-quantity-controls {
    display: flex;
    align-items: center;
    gap: 5px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 2px;
}

.wcm-pos-qty-decrease,
.wcm-pos-qty-increase {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #667eea;
}

.wcm-pos-qty-decrease:hover,
.wcm-pos-qty-increase:hover {
    background: #667eea;
    color: #fff;
    border-color: #667eea;
}

.wcm-pos-qty-input {
    width: 50px;
    text-align: center;
    border: none;
    background: transparent;
    font-size: 14px;
    font-weight: 600;
    color: #1d2327;
}

.wcm-pos-remove-item {
    background: rgba(214, 54, 56, 0.1);
    border: 1px solid rgba(214, 54, 56, 0.2);
    color: #d63638;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.wcm-pos-remove-item:hover {
    background: #d63638;
    color: #fff;
    transform: scale(1.1);
}

.wcm-pos-cart-item-total {
    text-align: right;
    font-size: 16px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Cart Totals */
.wcm-pos-cart-totals {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #f0f0f1;
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
}

.wcm-pos-subtotal,
.wcm-pos-tax,
.wcm-pos-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.wcm-pos-total {
    margin-bottom: 0;
    padding-top: 8px;
    border-top: 1px solid #f0f0f1;
    font-size: 18px;
    font-weight: 700;
}

.wcm-pos-total span:last-child {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Payment Section */
.wcm-pos-payment-section h3 {
    margin: 0 0 15px;
    font-size: 16px;
    font-weight: 700;
    color: #1d2327;
}

.wcm-pos-payment-methods {
    margin-bottom: 20px;
}

.wcm-pos-payment-method {
    display: block;
    margin-bottom: 10px;
    cursor: pointer;
}

.wcm-pos-payment-method input[type="radio"] {
    display: none;
}

.wcm-pos-payment-method-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #f0f0f1;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.wcm-pos-payment-method input[type="radio"]:checked + .wcm-pos-payment-method-content {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.wcm-pos-payment-method-content .dashicons {
    font-size: 20px;
    color: #667eea;
}

.wcm-pos-payment-method-title {
    font-size: 14px;
    font-weight: 600;
    color: #1d2327;
}

/* Customer Section */
.wcm-pos-customer-section {
    margin-bottom: 20px;
}

.wcm-pos-toggle-customer-btn {
    width: 100%;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #f0f0f1;
    padding: 12px 15px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #667eea;
}

.wcm-pos-toggle-customer-btn:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-color: #667eea;
    transform: translateY(-1px);
}

.wcm-pos-customer-form {
    margin-top: 15px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.wcm-pos-customer-form input {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.wcm-pos-customer-form input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    outline: none;
}

.wcm-pos-customer-form input[type="email"],
.wcm-pos-customer-form input[type="tel"] {
    grid-column: 1 / -1;
}

/* Checkout Button */
.wcm-pos-checkout-btn {
    width: 100%;
    background: linear-gradient(135deg, #00d4aa 0%, #00a32a 100%);
    color: #fff;
    border: none;
    padding: 15px 20px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.wcm-pos-checkout-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 163, 42, 0.3);
}

.wcm-pos-checkout-btn:disabled {
    background: #c3c4c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* POS Modals */
.wcm-pos-variation-modal-content,
.wcm-pos-receipt-modal-content {
    max-width: 600px;
}

.wcm-pos-variation-modal-content .wcm-modal-body {
    max-height: 400px;
    overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .wcm-pos-container {
        grid-template-columns: 1fr 350px;
    }
}

@media (max-width: 768px) {
    .wcm-pos-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
        height: auto;
        min-height: auto;
    }

    .wcm-pos-cart-section {
        order: -1;
        max-height: 400px;
    }

    .wcm-pos-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .wcm-pos-search-container {
        min-width: auto;
    }

    .wcm-pos-filter-container {
        min-width: auto;
    }

    .wcm-pos-products-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }

    .wcm-pos-customer-form {
        grid-template-columns: 1fr;
    }

    .wcm-pos-customer-form input[type="email"],
    .wcm-pos-customer-form input[type="tel"] {
        grid-column: auto;
    }
}

@media (max-width: 480px) {
    .wcm-pos-dashboard {
        padding: 0 10px;
    }

    .wcm-pos-container {
        gap: 15px;
        margin-top: 15px;
    }

    .wcm-pos-products-section,
    .wcm-pos-cart-section {
        padding: 15px;
    }

    .wcm-pos-products-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .wcm-pos-product-image {
        height: 80px;
    }

    .wcm-pos-product-name {
        font-size: 12px;
    }

    .wcm-pos-add-to-cart-btn,
    .wcm-pos-select-variation-btn {
        font-size: 11px;
        padding: 6px 8px;
    }
}

/* Loading States */
.wcm-pos-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    backdrop-filter: blur(2px);
}

.wcm-pos-processing {
    opacity: 0.7;
    pointer-events: none;
}

/* Animations */
@keyframes wcm-pos-fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.wcm-pos-product-card,
.wcm-pos-cart-item {
    animation: wcm-pos-fadeIn 0.3s ease-out;
}

/* Print Styles for Receipt */
@media print {
    .wcm-pos-receipt-modal-content {
        box-shadow: none;
        border: none;
        background: #fff;
    }

    .wcm-modal-header,
    .wcm-modal-footer {
        display: none;
    }

    .wcm-modal-body {
        padding: 0;
    }
}
