/**
 * Modern POS Interface for Woo Cash Manager
 */

/* Reset and Full Height Setup */
* {
    box-sizing: border-box;
}

/* Hide WordPress admin navigation on POS page */
body.woocommerce_page_woo-cash-manager-pos {
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    height: 100vh !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

body.woocommerce_page_woo-cash-manager-pos #adminmenumain,
body.woocommerce_page_woo-cash-manager-pos #wpadminbar,
body.woocommerce_page_woo-cash-manager-pos #wpfooter,
body.woocommerce_page_woo-cash-manager-pos .wp-header-end {
    display: none !important;
}

body.woocommerce_page_woo-cash-manager-pos #wpcontent {
    margin: 0 !important;
    padding: 0 !important;
    height: 100vh !important;
    overflow: hidden !important;
}

body.woocommerce_page_woo-cash-manager-pos #wpbody-content {
    padding: 0 !important;
    height: 100vh !important;
    overflow: hidden !important;
}

/* Modern POS Dashboard Layout */
.wcm-pos-dashboard {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: transparent;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

/* Header Section */
.wcm-pos-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 15px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    z-index: 100;
    position: relative;
}

.wcm-pos-header .wp-heading-inline {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
}

.wcm-pos-header .wp-heading-inline .dashicons {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 32px;
}

.wcm-pos-header .button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white !important;
    border: none;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.wcm-pos-header .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

/* Main POS Container - Full Height */
.wcm-pos-container {
    display: grid;
    grid-template-columns: 1fr 420px;
    gap: 0;
    height: calc(100vh - 80px);
    overflow: hidden;
    flex: 1;
}

/* Products Section - Left Panel */
.wcm-pos-products-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    margin: 20px;
    margin-right: 10px;
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 120px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Modern Filters Section */
.wcm-pos-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    align-items: center;
    flex-wrap: wrap;
    padding-bottom: 20px;
    border-bottom: 2px solid rgba(102, 126, 234, 0.1);
    flex-shrink: 0;
}

/* Modern Search Container */
.wcm-pos-search-container {
    display: flex;
    flex: 1;
    min-width: 300px;
    position: relative;
}

.wcm-pos-search-input {
    flex: 1;
    padding: 15px 50px 15px 20px;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 30px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    font-weight: 500;
}

.wcm-pos-search-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    outline: none;
    background: rgba(255, 255, 255, 1);
}

.wcm-pos-search-input::placeholder {
    color: #8e9aaf;
    font-weight: 400;
}

.wcm-pos-search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.wcm-pos-search-btn:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
}

/* Modern Filter Select */
.wcm-pos-filter-container {
    min-width: 200px;
}

.wcm-pos-filter-select {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 15px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    font-weight: 500;
    cursor: pointer;
}

.wcm-pos-filter-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    outline: none;
    background: rgba(255, 255, 255, 1);
}

/* Modern View Toggle */
.wcm-pos-view-toggle {
    display: flex;
    gap: 8px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 8px;
    border: 2px solid rgba(102, 126, 234, 0.2);
}

.wcm-pos-view-btn {
    background: transparent;
    border: none;
    padding: 12px 16px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #8e9aaf;
    font-weight: 600;
}

.wcm-pos-view-btn.active,
.wcm-pos-view-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Products Container - Scrollable */
.wcm-pos-products-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    padding-right: 10px;
    margin-right: -10px;
}

/* Custom Scrollbar */
.wcm-pos-products-container::-webkit-scrollbar {
    width: 8px;
}

.wcm-pos-products-container::-webkit-scrollbar-track {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
}

.wcm-pos-products-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}

.wcm-pos-products-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.wcm-pos-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px;
    color: #8e9aaf;
    font-weight: 500;
}

.wcm-pos-loading .wcm-spinner {
    margin-bottom: 20px;
}

/* Modern Products Grid */
.wcm-pos-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    padding: 15px 0;
}

/* Modern Product Card */
.wcm-pos-product-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(102, 126, 234, 0.1);
    border-radius: 20px;
    padding: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.wcm-pos-product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.wcm-pos-product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.3);
    background: rgba(255, 255, 255, 1);
}

.wcm-pos-product-card:hover::before {
    opacity: 1;
}

.wcm-pos-product-image {
    position: relative;
    width: 100%;
    height: 140px;
    margin-bottom: 15px;
    border-radius: 15px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.wcm-pos-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.wcm-pos-product-card:hover .wcm-pos-product-image img {
    transform: scale(1.1);
}

.wcm-pos-no-image {
    color: #8e9aaf;
    font-size: 45px;
    opacity: 0.7;
}

.wcm-pos-sale-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: linear-gradient(135deg, #ff6b6b 0%, #d63638 100%);
    color: #fff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.wcm-pos-product-info {
    text-align: center;
}

.wcm-pos-product-name {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 8px;
    color: #1d2327;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.wcm-pos-product-sku {
    font-size: 11px;
    color: #646970;
    margin-bottom: 8px;
    font-weight: 500;
}

.wcm-pos-product-price {
    margin-bottom: 8px;
}

.wcm-pos-regular-price {
    text-decoration: line-through;
    color: #646970;
    font-size: 12px;
    margin-right: 5px;
}

.wcm-pos-sale-price,
.wcm-pos-current-price {
    font-size: 16px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.wcm-pos-product-stock {
    margin-bottom: 12px;
    font-size: 11px;
    color: #646970;
}

.wcm-pos-stock-qty {
    background: rgba(0, 163, 42, 0.1);
    color: #00a32a;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 600;
}

.wcm-pos-product-actions {
    margin-top: 12px;
}

.wcm-pos-add-to-cart-btn,
.wcm-pos-select-variation-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.wcm-pos-add-to-cart-btn:hover,
.wcm-pos-select-variation-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Load More */
.wcm-pos-load-more-container {
    text-align: center;
    padding: 20px 0;
}

.wcm-pos-load-more-btn {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 1px solid #e1e5e9;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #667eea;
}

.wcm-pos-load-more-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

/* Modern Cart Section - Right Panel */
.wcm-pos-cart-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    margin: 20px;
    margin-left: 10px;
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    height: calc(100vh - 120px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.wcm-pos-cart-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.wcm-pos-cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid rgba(102, 126, 234, 0.1);
    flex-shrink: 0;
}

.wcm-pos-cart-header h2 {
    margin: 0;
    font-size: 22px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.wcm-pos-cart-header h2 .dashicons {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 24px;
}

.wcm-pos-clear-cart-btn {
    background: rgba(220, 53, 69, 0.1);
    border: 2px solid rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border-radius: 50%;
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.wcm-pos-clear-cart-btn:hover {
    background: #dc3545;
    color: #fff;
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

/* Modern Cart Items Container */
.wcm-pos-cart-items {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    margin-bottom: 25px;
    padding-right: 10px;
    margin-right: -10px;
}

/* Custom Scrollbar for Cart Items */
.wcm-pos-cart-items::-webkit-scrollbar {
    width: 6px;
}

.wcm-pos-cart-items::-webkit-scrollbar-track {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
}

.wcm-pos-cart-items::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}

.wcm-pos-empty-cart {
    text-align: center;
    padding: 60px 20px;
    color: #8e9aaf;
}

.wcm-pos-empty-cart .dashicons {
    font-size: 60px;
    color: rgba(102, 126, 234, 0.3);
    margin-bottom: 20px;
}

.wcm-pos-empty-cart p {
    margin: 15px 0 8px;
    font-size: 18px;
    font-weight: 600;
    color: #6c757d;
}

.wcm-pos-empty-cart small {
    font-size: 14px;
    color: #8e9aaf;
}

/* Modern Cart Item */
.wcm-pos-cart-item {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(102, 126, 234, 0.1);
    border-radius: 16px;
    padding: 18px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.wcm-pos-cart-item:hover {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(102, 126, 234, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.wcm-pos-cart-item-info h4 {
    margin: 0 0 8px;
    font-size: 16px;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1.3;
}

.wcm-pos-cart-item-info small {
    color: #8e9aaf;
    font-size: 12px;
    font-weight: 500;
}

.wcm-pos-cart-item-price {
    font-size: 15px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-top: 8px;
}

.wcm-pos-cart-item-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0 10px;
}

.wcm-pos-quantity-controls {
    display: flex;
    align-items: center;
    gap: 5px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 2px;
}

.wcm-pos-qty-decrease,
.wcm-pos-qty-increase {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #667eea;
}

.wcm-pos-qty-decrease:hover,
.wcm-pos-qty-increase:hover {
    background: #667eea;
    color: #fff;
    border-color: #667eea;
}

.wcm-pos-qty-input {
    width: 50px;
    text-align: center;
    border: none;
    background: transparent;
    font-size: 14px;
    font-weight: 600;
    color: #1d2327;
}

.wcm-pos-remove-item {
    background: rgba(214, 54, 56, 0.1);
    border: 1px solid rgba(214, 54, 56, 0.2);
    color: #d63638;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.wcm-pos-remove-item:hover {
    background: #d63638;
    color: #fff;
    transform: scale(1.1);
}

.wcm-pos-cart-item-total {
    text-align: right;
    font-size: 16px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Modern Cart Totals */
.wcm-pos-cart-totals {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(102, 126, 234, 0.1);
    border-radius: 18px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    flex-shrink: 0;
}

.wcm-pos-subtotal,
.wcm-pos-tax,
.wcm-pos-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
}

.wcm-pos-subtotal span:first-child,
.wcm-pos-tax span:first-child {
    color: #6c757d;
}

.wcm-pos-subtotal span:last-child,
.wcm-pos-tax span:last-child {
    color: #2c3e50;
    font-weight: 700;
}

.wcm-pos-total {
    margin-bottom: 0;
    padding-top: 15px;
    border-top: 2px solid rgba(102, 126, 234, 0.2);
    font-size: 20px;
    font-weight: 800;
}

.wcm-pos-total span:first-child {
    color: #2c3e50;
}

.wcm-pos-total span:last-child {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 22px;
}

/* Modern Payment Section */
.wcm-pos-payment-section {
    flex-shrink: 0;
}

.wcm-pos-payment-section h3 {
    margin: 0 0 20px;
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.wcm-pos-payment-section h3::before {
    content: '💳';
    font-size: 20px;
}

.wcm-pos-payment-methods {
    margin-bottom: 25px;
}

/* Modern Payment Methods */
.wcm-pos-payment-method {
    display: block;
    margin-bottom: 15px;
    cursor: pointer;
}

.wcm-pos-payment-method input[type="radio"] {
    display: none;
}

.wcm-pos-payment-method-content {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(102, 126, 234, 0.1);
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.wcm-pos-payment-method-content::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.wcm-pos-payment-method input[type="radio"]:checked + .wcm-pos-payment-method-content {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
    border-color: rgba(102, 126, 234, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.wcm-pos-payment-method input[type="radio"]:checked + .wcm-pos-payment-method-content::before {
    opacity: 1;
}

.wcm-pos-payment-method-content .dashicons {
    font-size: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.wcm-pos-payment-method-title {
    font-size: 16px;
    font-weight: 700;
    color: #2c3e50;
}

/* Modern Customer Section */
.wcm-pos-customer-section {
    margin-bottom: 25px;
}

.wcm-pos-toggle-customer-btn {
    width: 100%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(102, 126, 234, 0.2);
    padding: 16px 20px;
    border-radius: 15px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.wcm-pos-toggle-customer-btn:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    border-color: #667eea;
    transform: translateY(-1px);
}

.wcm-pos-customer-form {
    margin-top: 15px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.wcm-pos-customer-form input {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.wcm-pos-customer-form input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    outline: none;
}

.wcm-pos-customer-form input[type="email"],
.wcm-pos-customer-form input[type="tel"] {
    grid-column: 1 / -1;
}

/* Modern Checkout Button */
.wcm-pos-checkout-btn {
    width: 100%;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: #fff;
    border: none;
    padding: 20px 25px;
    border-radius: 18px;
    font-size: 18px;
    font-weight: 800;
    cursor: pointer;
    transition: all 0.4s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 8px 30px rgba(40, 167, 69, 0.3);
    position: relative;
    overflow: hidden;
}

.wcm-pos-checkout-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.wcm-pos-checkout-btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(40, 167, 69, 0.4);
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
}

.wcm-pos-checkout-btn:hover:not(:disabled)::before {
    left: 100%;
}

.wcm-pos-checkout-btn:disabled {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.2);
}

.wcm-pos-checkout-btn .dashicons {
    font-size: 20px;
}

/* POS Modals */
.wcm-pos-variation-modal-content,
.wcm-pos-receipt-modal-content {
    max-width: 600px;
}

.wcm-pos-variation-modal-content .wcm-modal-body {
    max-height: 400px;
    overflow-y: auto;
}

/* Modern Responsive Design */
@media (max-width: 1400px) {
    .wcm-pos-container {
        grid-template-columns: 1fr 380px;
    }
}

@media (max-width: 1200px) {
    .wcm-pos-container {
        grid-template-columns: 1fr 350px;
    }

    .wcm-pos-products-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 992px) {
    body.woocommerce_page_woo-cash-manager-pos {
        overflow: auto !important;
    }

    .wcm-pos-container {
        grid-template-columns: 1fr;
        height: auto;
        gap: 20px;
    }

    .wcm-pos-cart-section {
        order: -1;
        height: auto;
        max-height: 500px;
        margin: 20px;
    }

    .wcm-pos-products-section {
        height: auto;
        min-height: 600px;
    }

    .wcm-pos-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .wcm-pos-search-container,
    .wcm-pos-filter-container {
        min-width: auto;
    }

    .wcm-pos-products-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
}

@media (max-width: 768px) {
    .wcm-pos-header {
        padding: 12px 20px;
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .wcm-pos-header .wp-heading-inline {
        font-size: 24px;
        justify-content: center;
    }

    .wcm-pos-products-section,
    .wcm-pos-cart-section {
        margin: 15px;
        padding: 20px;
    }

    .wcm-pos-products-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 15px;
    }

    .wcm-pos-customer-form {
        grid-template-columns: 1fr;
    }

    .wcm-pos-customer-form input[type="email"],
    .wcm-pos-customer-form input[type="tel"] {
        grid-column: auto;
    }
}

@media (max-width: 480px) {
    .wcm-pos-header {
        padding: 10px 15px;
    }

    .wcm-pos-header .wp-heading-inline {
        font-size: 20px;
    }

    .wcm-pos-products-section,
    .wcm-pos-cart-section {
        margin: 10px;
        padding: 15px;
        border-radius: 15px;
    }

    .wcm-pos-products-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 12px;
    }

    .wcm-pos-product-image {
        height: 100px;
    }

    .wcm-pos-product-name {
        font-size: 13px;
    }

    .wcm-pos-add-to-cart-btn,
    .wcm-pos-select-variation-btn {
        font-size: 12px;
        padding: 8px 12px;
    }

    .wcm-pos-search-input,
    .wcm-pos-filter-select {
        padding: 12px 15px;
        font-size: 14px;
    }
}

/* Modern Loading States */
.wcm-pos-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 100;
    animation: wcm-pos-fadeIn 0.3s ease;
}

.wcm-pos-processing {
    opacity: 0.7;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

/* Modern Spinner */
.wcm-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.2);
    border-left: 4px solid #667eea;
    border-radius: 50%;
    animation: wcm-spin 1s linear infinite;
    margin-bottom: 15px;
}

/* Smooth Animations */
@keyframes wcm-pos-fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes wcm-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes wcm-pos-slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes wcm-pos-pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Apply animations to elements */
.wcm-pos-product-card {
    animation: wcm-pos-fadeIn 0.4s ease forwards;
}

.wcm-pos-cart-item {
    animation: wcm-pos-slideUp 0.3s ease forwards;
}

.wcm-pos-product-card:nth-child(even) {
    animation-delay: 0.1s;
}

.wcm-pos-product-card:nth-child(3n) {
    animation-delay: 0.2s;
}

/* Notification Styles */
.wcm-pos-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 15px 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    animation: wcm-pos-slideUp 0.3s ease;
    border-left: 4px solid #28a745;
}

.wcm-pos-notification.error {
    border-left-color: #dc3545;
}

.wcm-pos-notification.warning {
    border-left-color: #ffc107;
}

.wcm-pos-product-card,
.wcm-pos-cart-item {
    animation: wcm-pos-fadeIn 0.3s ease-out;
}

/* Print Styles for Receipt */
@media print {
    .wcm-pos-receipt-modal-content {
        box-shadow: none;
        border: none;
        background: #fff;
    }

    .wcm-modal-header,
    .wcm-modal-footer {
        display: none;
    }

    .wcm-modal-body {
        padding: 0;
    }
}
