<?php
/**
 * POS Receipt Template
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// This template can be used for server-side receipt generation if needed
// Currently, receipts are generated client-side in JavaScript for immediate printing

if (!isset($order) || !$order) {
    return;
}

$order_items = $order->get_items();
$billing_address = $order->get_formatted_billing_address();
?>

<div class="wcm-pos-receipt-template">
    <div class="wcm-receipt-header">
        <h1><?php echo get_bloginfo('name'); ?></h1>
        <?php if (get_bloginfo('description')): ?>
            <p class="wcm-receipt-tagline"><?php echo get_bloginfo('description'); ?></p>
        <?php endif; ?>
        
        <div class="wcm-receipt-store-info">
            <?php
            // Get store address from WooCommerce settings
            $store_address = get_option('woocommerce_store_address');
            $store_address_2 = get_option('woocommerce_store_address_2');
            $store_city = get_option('woocommerce_store_city');
            $store_postcode = get_option('woocommerce_store_postcode');
            $store_country = get_option('woocommerce_default_country');
            
            if ($store_address): ?>
                <p><?php echo esc_html($store_address); ?></p>
            <?php endif; ?>
            
            <?php if ($store_address_2): ?>
                <p><?php echo esc_html($store_address_2); ?></p>
            <?php endif; ?>
            
            <?php if ($store_city || $store_postcode): ?>
                <p>
                    <?php echo esc_html($store_city); ?>
                    <?php if ($store_city && $store_postcode): ?>, <?php endif; ?>
                    <?php echo esc_html($store_postcode); ?>
                </p>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="wcm-receipt-divider"></div>
    
    <div class="wcm-receipt-order-info">
        <div class="wcm-receipt-row">
            <span class="wcm-receipt-label"><?php _e('Order #:', 'woo-cash-manager'); ?></span>
            <span class="wcm-receipt-value"><?php echo $order->get_order_number(); ?></span>
        </div>
        
        <div class="wcm-receipt-row">
            <span class="wcm-receipt-label"><?php _e('Date:', 'woo-cash-manager'); ?></span>
            <span class="wcm-receipt-value"><?php echo $order->get_date_created()->format('Y-m-d H:i:s'); ?></span>
        </div>
        
        <div class="wcm-receipt-row">
            <span class="wcm-receipt-label"><?php _e('Cashier:', 'woo-cash-manager'); ?></span>
            <span class="wcm-receipt-value"><?php echo wp_get_current_user()->display_name; ?></span>
        </div>
        
        <?php if ($order->get_payment_method()): ?>
            <div class="wcm-receipt-row">
                <span class="wcm-receipt-label"><?php _e('Payment:', 'woo-cash-manager'); ?></span>
                <span class="wcm-receipt-value"><?php echo $order->get_payment_method_title(); ?></span>
            </div>
        <?php endif; ?>
    </div>
    
    <?php if ($billing_address): ?>
        <div class="wcm-receipt-divider"></div>
        
        <div class="wcm-receipt-customer-info">
            <h3><?php _e('Customer Information', 'woo-cash-manager'); ?></h3>
            <div class="wcm-receipt-address">
                <?php echo $billing_address; ?>
            </div>
            
            <?php if ($order->get_billing_email()): ?>
                <div class="wcm-receipt-row">
                    <span class="wcm-receipt-label"><?php _e('Email:', 'woo-cash-manager'); ?></span>
                    <span class="wcm-receipt-value"><?php echo $order->get_billing_email(); ?></span>
                </div>
            <?php endif; ?>
            
            <?php if ($order->get_billing_phone()): ?>
                <div class="wcm-receipt-row">
                    <span class="wcm-receipt-label"><?php _e('Phone:', 'woo-cash-manager'); ?></span>
                    <span class="wcm-receipt-value"><?php echo $order->get_billing_phone(); ?></span>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    
    <div class="wcm-receipt-divider"></div>
    
    <div class="wcm-receipt-items">
        <h3><?php _e('Items', 'woo-cash-manager'); ?></h3>
        
        <div class="wcm-receipt-items-header">
            <span class="wcm-receipt-item-name"><?php _e('Item', 'woo-cash-manager'); ?></span>
            <span class="wcm-receipt-item-qty"><?php _e('Qty', 'woo-cash-manager'); ?></span>
            <span class="wcm-receipt-item-price"><?php _e('Price', 'woo-cash-manager'); ?></span>
            <span class="wcm-receipt-item-total"><?php _e('Total', 'woo-cash-manager'); ?></span>
        </div>
        
        <?php foreach ($order_items as $item_id => $item): ?>
            <?php
            $product = $item->get_product();
            $quantity = $item->get_quantity();
            $line_total = $item->get_total();
            $unit_price = $line_total / $quantity;
            ?>
            
            <div class="wcm-receipt-item">
                <span class="wcm-receipt-item-name">
                    <?php echo $item->get_name(); ?>
                    <?php if ($product && $product->get_sku()): ?>
                        <small>(<?php echo $product->get_sku(); ?>)</small>
                    <?php endif; ?>
                </span>
                <span class="wcm-receipt-item-qty"><?php echo $quantity; ?></span>
                <span class="wcm-receipt-item-price"><?php echo wc_price($unit_price); ?></span>
                <span class="wcm-receipt-item-total"><?php echo wc_price($line_total); ?></span>
            </div>
        <?php endforeach; ?>
    </div>
    
    <div class="wcm-receipt-divider"></div>
    
    <div class="wcm-receipt-totals">
        <div class="wcm-receipt-row">
            <span class="wcm-receipt-label"><?php _e('Subtotal:', 'woo-cash-manager'); ?></span>
            <span class="wcm-receipt-value"><?php echo wc_price($order->get_subtotal()); ?></span>
        </div>
        
        <?php if ($order->get_total_tax() > 0): ?>
            <div class="wcm-receipt-row">
                <span class="wcm-receipt-label"><?php _e('Tax:', 'woo-cash-manager'); ?></span>
                <span class="wcm-receipt-value"><?php echo wc_price($order->get_total_tax()); ?></span>
            </div>
        <?php endif; ?>
        
        <?php if ($order->get_total_discount() > 0): ?>
            <div class="wcm-receipt-row">
                <span class="wcm-receipt-label"><?php _e('Discount:', 'woo-cash-manager'); ?></span>
                <span class="wcm-receipt-value">-<?php echo wc_price($order->get_total_discount()); ?></span>
            </div>
        <?php endif; ?>
        
        <div class="wcm-receipt-row wcm-receipt-total">
            <span class="wcm-receipt-label"><?php _e('Total:', 'woo-cash-manager'); ?></span>
            <span class="wcm-receipt-value"><?php echo wc_price($order->get_total()); ?></span>
        </div>
        
        <?php if ($order->get_payment_method() === 'cash'): ?>
            <div class="wcm-receipt-row">
                <span class="wcm-receipt-label"><?php _e('Cash Received:', 'woo-cash-manager'); ?></span>
                <span class="wcm-receipt-value"><?php echo wc_price($order->get_total()); ?></span>
            </div>
            
            <div class="wcm-receipt-row">
                <span class="wcm-receipt-label"><?php _e('Change:', 'woo-cash-manager'); ?></span>
                <span class="wcm-receipt-value"><?php echo wc_price(0); ?></span>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="wcm-receipt-divider"></div>
    
    <div class="wcm-receipt-footer">
        <p class="wcm-receipt-thank-you"><?php _e('Thank you for your purchase!', 'woo-cash-manager'); ?></p>
        
        <?php if ($order->get_status() === 'completed'): ?>
            <p class="wcm-receipt-status"><?php _e('Transaction Completed', 'woo-cash-manager'); ?></p>
        <?php endif; ?>
        
        <p class="wcm-receipt-timestamp">
            <?php echo sprintf(__('Printed on %s', 'woo-cash-manager'), current_time('Y-m-d H:i:s')); ?>
        </p>
        
        <?php
        // Add any custom footer text from settings
        $footer_text = get_option('wcm_pos_receipt_footer', '');
        if ($footer_text): ?>
            <div class="wcm-receipt-custom-footer">
                <?php echo wp_kses_post($footer_text); ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style type="text/css" media="print">
    .wcm-pos-receipt-template {
        font-family: 'Courier New', monospace;
        max-width: 300px;
        margin: 0 auto;
        padding: 10px;
        font-size: 12px;
        line-height: 1.4;
    }
    
    .wcm-receipt-header {
        text-align: center;
        margin-bottom: 15px;
    }
    
    .wcm-receipt-header h1 {
        margin: 0 0 5px;
        font-size: 16px;
        font-weight: bold;
    }
    
    .wcm-receipt-tagline {
        margin: 0 0 10px;
        font-size: 10px;
        font-style: italic;
    }
    
    .wcm-receipt-store-info {
        font-size: 10px;
    }
    
    .wcm-receipt-store-info p {
        margin: 2px 0;
    }
    
    .wcm-receipt-divider {
        border-top: 1px dashed #000;
        margin: 10px 0;
    }
    
    .wcm-receipt-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 3px;
    }
    
    .wcm-receipt-label {
        font-weight: bold;
    }
    
    .wcm-receipt-items-header {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr;
        gap: 5px;
        font-weight: bold;
        border-bottom: 1px solid #000;
        padding-bottom: 3px;
        margin-bottom: 5px;
        font-size: 10px;
    }
    
    .wcm-receipt-item {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr;
        gap: 5px;
        margin-bottom: 3px;
        font-size: 10px;
    }
    
    .wcm-receipt-item-name small {
        display: block;
        font-size: 9px;
        color: #666;
    }
    
    .wcm-receipt-total {
        font-weight: bold;
        font-size: 14px;
        border-top: 1px solid #000;
        padding-top: 5px;
        margin-top: 5px;
    }
    
    .wcm-receipt-footer {
        text-align: center;
        margin-top: 15px;
    }
    
    .wcm-receipt-thank-you {
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .wcm-receipt-timestamp {
        font-size: 9px;
        color: #666;
        margin-top: 10px;
    }
    
    .wcm-receipt-custom-footer {
        margin-top: 10px;
        font-size: 10px;
    }
</style>
