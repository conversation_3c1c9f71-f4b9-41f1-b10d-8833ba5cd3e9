<?php
/**
 * POS AJAX functionality for Woo Cash Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WCM_POS_Ajax {
    
    /**
     * Constructor
     */
    public function __construct() {
        // AJAX actions for logged-in users
        add_action('wp_ajax_wcm_pos_search_products', array($this, 'search_products'));
        add_action('wp_ajax_wcm_pos_get_products', array($this, 'get_products'));
        add_action('wp_ajax_wcm_pos_add_to_cart', array($this, 'add_to_cart'));
        add_action('wp_ajax_wcm_pos_remove_from_cart', array($this, 'remove_from_cart'));
        add_action('wp_ajax_wcm_pos_update_cart', array($this, 'update_cart'));
        add_action('wp_ajax_wcm_pos_clear_cart', array($this, 'clear_cart'));
        add_action('wp_ajax_wcm_pos_get_cart', array($this, 'get_cart'));
        add_action('wp_ajax_wcm_pos_create_order', array($this, 'create_order'));
        add_action('wp_ajax_wcm_pos_get_product_details', array($this, 'get_product_details'));
    }
    
    /**
     * Search products via AJAX
     */
    public function search_products() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        $search_term = sanitize_text_field($_POST['search_term'] ?? '');
        $limit = intval($_POST['limit'] ?? 10);
        
        if (!class_exists('WCM_POS')) {
            require_once WCM_PLUGIN_PATH . 'includes/class-wcm-pos.php';
        }
        
        $pos = new WCM_POS();
        $products = $pos->search_products($search_term, $limit);
        
        wp_send_json_success($products);
    }
    
    /**
     * Get products for POS display
     */
    public function get_products() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        $search = sanitize_text_field($_POST['search'] ?? '');
        $category = sanitize_text_field($_POST['category'] ?? '');
        $limit = intval($_POST['limit'] ?? 20);
        $offset = intval($_POST['offset'] ?? 0);
        
        if (!class_exists('WCM_POS')) {
            require_once WCM_PLUGIN_PATH . 'includes/class-wcm-pos.php';
        }
        
        $pos = new WCM_POS();
        $products = $pos->get_pos_products($search, $category, $limit, $offset);
        
        wp_send_json_success($products);
    }
    
    /**
     * Add product to cart
     */
    public function add_to_cart() {
        try {
            // Check nonce
            if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
                wp_send_json_error(__('Security check failed', 'woo-cash-manager'));
                return;
            }

            // Check capabilities
            if (!current_user_can('manage_woocommerce')) {
                wp_send_json_error(__('Insufficient permissions', 'woo-cash-manager'));
                return;
            }

            $product_id = intval($_POST['product_id']);
            $quantity = intval($_POST['quantity'] ?? 1);
            $variation_id = intval($_POST['variation_id'] ?? 0);
            $variation_data = $_POST['variation_data'] ?? array();

            if (!$product_id) {
                wp_send_json_error(__('Invalid product ID', 'woo-cash-manager'));
                return;
            }

            // Validate product exists
            $product = wc_get_product($product_id);
            if (!$product) {
                wp_send_json_error(__('Product not found', 'woo-cash-manager'));
                return;
            }

            if (!class_exists('WCM_POS')) {
                require_once WCM_PLUGIN_PATH . 'includes/class-wcm-pos.php';
            }

            $pos = new WCM_POS();
            $result = $pos->add_to_cart($product_id, $quantity, $variation_id, $variation_data);

            if ($result) {
                $cart_data = array(
                    'items' => $pos->get_cart_items(),
                    'totals' => $pos->get_cart_totals()
                );

                // Debug logging
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('WCM POS: Cart data being sent: ' . print_r($cart_data, true));
                }

                wp_send_json_success($cart_data);
            } else {
                wp_send_json_error(__('Failed to add product to cart. Please check stock availability.', 'woo-cash-manager'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('An error occurred: ', 'woo-cash-manager') . $e->getMessage());
        }
    }
    
    /**
     * Remove item from cart
     */
    public function remove_from_cart() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        $cart_item_key = sanitize_text_field($_POST['cart_item_key']);
        
        if (!$cart_item_key) {
            wp_send_json_error(__('Invalid cart item key', 'woo-cash-manager'));
        }
        
        if (!class_exists('WCM_POS')) {
            require_once WCM_PLUGIN_PATH . 'includes/class-wcm-pos.php';
        }
        
        $pos = new WCM_POS();
        $result = $pos->remove_from_cart($cart_item_key);
        
        if ($result) {
            $cart_data = array(
                'items' => $pos->get_cart_items(),
                'totals' => $pos->get_cart_totals()
            );
            wp_send_json_success($cart_data);
        } else {
            wp_send_json_error(__('Failed to remove item from cart', 'woo-cash-manager'));
        }
    }
    
    /**
     * Update cart item quantity
     */
    public function update_cart() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        $cart_item_key = sanitize_text_field($_POST['cart_item_key']);
        $quantity = intval($_POST['quantity']);
        
        if (!$cart_item_key) {
            wp_send_json_error(__('Invalid cart item key', 'woo-cash-manager'));
        }
        
        if (!class_exists('WCM_POS')) {
            require_once WCM_PLUGIN_PATH . 'includes/class-wcm-pos.php';
        }
        
        $pos = new WCM_POS();
        $result = $pos->update_cart_quantity($cart_item_key, $quantity);
        
        if ($result) {
            $cart_data = array(
                'items' => $pos->get_cart_items(),
                'totals' => $pos->get_cart_totals()
            );
            wp_send_json_success($cart_data);
        } else {
            wp_send_json_error(__('Failed to update cart', 'woo-cash-manager'));
        }
    }
    
    /**
     * Clear cart
     */
    public function clear_cart() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        if (!class_exists('WCM_POS')) {
            require_once WCM_PLUGIN_PATH . 'includes/class-wcm-pos.php';
        }
        
        $pos = new WCM_POS();
        $result = $pos->clear_cart();
        
        if ($result) {
            wp_send_json_success(__('Cart cleared successfully', 'woo-cash-manager'));
        } else {
            wp_send_json_error(__('Failed to clear cart', 'woo-cash-manager'));
        }
    }
    
    /**
     * Get current cart data
     */
    public function get_cart() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        if (!class_exists('WCM_POS')) {
            require_once WCM_PLUGIN_PATH . 'includes/class-wcm-pos.php';
        }
        
        $pos = new WCM_POS();
        $cart_data = array(
            'items' => $pos->get_cart_items(),
            'totals' => $pos->get_cart_totals()
        );
        
        wp_send_json_success($cart_data);
    }
    
    /**
     * Create order from cart
     */
    public function create_order() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        $payment_method = sanitize_text_field($_POST['payment_method'] ?? 'cash');
        $customer_data = $_POST['customer_data'] ?? array();
        
        // Sanitize customer data
        if (!empty($customer_data)) {
            $customer_data = array(
                'first_name' => sanitize_text_field($customer_data['first_name'] ?? ''),
                'last_name' => sanitize_text_field($customer_data['last_name'] ?? ''),
                'email' => sanitize_email($customer_data['email'] ?? ''),
                'phone' => sanitize_text_field($customer_data['phone'] ?? '')
            );
        }
        
        if (!class_exists('WCM_POS')) {
            require_once WCM_PLUGIN_PATH . 'includes/class-wcm-pos.php';
        }
        
        $pos = new WCM_POS();
        $order_id = $pos->create_order($payment_method, $customer_data);
        
        if ($order_id) {
            $order = wc_get_order($order_id);
            wp_send_json_success(array(
                'order_id' => $order_id,
                'order_number' => $order->get_order_number(),
                'total' => $order->get_total(),
                'formatted_total' => wc_price($order->get_total()),
                'status' => $order->get_status(),
                'message' => __('Order created successfully!', 'woo-cash-manager')
            ));
        } else {
            wp_send_json_error(__('Failed to create order', 'woo-cash-manager'));
        }
    }
    
    /**
     * Get detailed product information
     */
    public function get_product_details() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        $product_id = intval($_POST['product_id']);
        
        if (!$product_id) {
            wp_send_json_error(__('Invalid product ID', 'woo-cash-manager'));
        }
        
        $product = wc_get_product($product_id);
        
        if (!$product) {
            wp_send_json_error(__('Product not found', 'woo-cash-manager'));
        }
        
        $product_data = array(
            'id' => $product->get_id(),
            'name' => $product->get_name(),
            'description' => $product->get_short_description(),
            'price' => $product->get_price(),
            'regular_price' => $product->get_regular_price(),
            'sale_price' => $product->get_sale_price(),
            'formatted_price' => wc_price($product->get_price()),
            'sku' => $product->get_sku(),
            'stock_quantity' => $product->get_stock_quantity(),
            'stock_status' => $product->get_stock_status(),
            'image_url' => wp_get_attachment_image_url($product->get_image_id(), 'medium'),
            'gallery_images' => $this->get_product_gallery_images($product),
            'attributes' => $product->get_attributes(),
            'type' => $product->get_type()
        );
        
        if ($product->is_type('variable')) {
            if (!class_exists('WCM_POS')) {
                require_once WCM_PLUGIN_PATH . 'includes/class-wcm-pos.php';
            }
            $pos = new WCM_POS();
            $product_data['variations'] = $pos->get_product_variations($product_id);
        }
        
        wp_send_json_success($product_data);
    }
    
    /**
     * Get product gallery images
     */
    private function get_product_gallery_images($product) {
        $gallery_images = array();
        $attachment_ids = $product->get_gallery_image_ids();
        
        foreach ($attachment_ids as $attachment_id) {
            $gallery_images[] = array(
                'id' => $attachment_id,
                'url' => wp_get_attachment_image_url($attachment_id, 'medium'),
                'thumbnail' => wp_get_attachment_image_url($attachment_id, 'thumbnail')
            );
        }
        
        return $gallery_images;
    }
}
