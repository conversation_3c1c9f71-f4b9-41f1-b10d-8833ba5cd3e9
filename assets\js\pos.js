/**
 * POS JavaScript for Woo Cash Manager
 */

(function($) {
    'use strict';
    
    var WCMPOS = {
        currentOffset: 0,
        isLoading: false,
        searchTimeout: null,
        
        init: function() {
            this.bindEvents();
            this.updateCartDisplay();
        },
        
        bindEvents: function() {
            // Product search
            $(document).on('input', '#wcm-pos-search', this.handleSearch);
            $(document).on('click', '#wcm-pos-search-btn', this.performSearch);
            
            // Category filter
            $(document).on('change', '#wcm-pos-category-filter', this.filterProducts);
            
            // View toggle
            $(document).on('click', '.wcm-pos-view-btn', this.toggleView);
            
            // Product actions
            $(document).on('click', '.wcm-pos-add-to-cart-btn', this.addToCart);
            $(document).on('click', '.wcm-pos-select-variation-btn', this.showVariationModal);
            
            // Cart actions
            $(document).on('click', '.wcm-pos-qty-decrease', this.decreaseQuantity);
            $(document).on('click', '.wcm-pos-qty-increase', this.increaseQuantity);
            $(document).on('change', '.wcm-pos-qty-input', this.updateQuantity);
            $(document).on('click', '.wcm-pos-remove-item', this.removeFromCart);
            $(document).on('click', '#wcm-pos-clear-cart', this.clearCart);
            
            // Customer info toggle
            $(document).on('click', '#wcm-pos-toggle-customer', this.toggleCustomerForm);
            
            // Checkout
            $(document).on('click', '#wcm-pos-checkout', this.processCheckout);
            
            // Load more products
            $(document).on('click', '#wcm-pos-load-more', this.loadMoreProducts);
            
            // Modal events
            $(document).on('click', '.wcm-modal-close', this.hideModal);
            $(document).on('click', '#wcm-pos-variation-cancel', this.hideModal);
            $(document).on('click', '#wcm-pos-variation-add-to-cart', this.addVariationToCart);
            $(document).on('click', '#wcm-pos-print-receipt', this.printReceipt);
            $(document).on('click', '#wcm-pos-new-sale', this.startNewSale);
            
            // Close modal on outside click
            $(document).on('click', '.wcm-modal', function(e) {
                if (e.target === this) {
                    WCMPOS.hideModal();
                }
            });
            
            // Enter key for search
            $(document).on('keypress', '#wcm-pos-search', function(e) {
                if (e.which === 13) {
                    WCMPOS.performSearch();
                }
            });
        },
        
        handleSearch: function() {
            var searchTerm = $(this).val();
            
            // Clear previous timeout
            if (WCMPOS.searchTimeout) {
                clearTimeout(WCMPOS.searchTimeout);
            }
            
            // Set new timeout for search
            WCMPOS.searchTimeout = setTimeout(function() {
                if (searchTerm.length >= 2 || searchTerm.length === 0) {
                    WCMPOS.performSearch();
                }
            }, 500);
        },
        
        performSearch: function() {
            var searchTerm = $('#wcm-pos-search').val();
            var category = $('#wcm-pos-category-filter').val();
            
            WCMPOS.currentOffset = 0;
            WCMPOS.loadProducts(searchTerm, category, true);
        },
        
        filterProducts: function() {
            WCMPOS.performSearch();
        },
        
        toggleView: function() {
            var view = $(this).data('view');
            
            $('.wcm-pos-view-btn').removeClass('active');
            $(this).addClass('active');
            
            var $grid = $('.wcm-pos-products-grid');
            
            if (view === 'list') {
                $grid.addClass('wcm-pos-list-view');
            } else {
                $grid.removeClass('wcm-pos-list-view');
            }
        },
        
        loadProducts: function(search, category, replace) {
            if (WCMPOS.isLoading) return;
            
            WCMPOS.isLoading = true;
            
            if (replace) {
                $('#wcm-pos-loading').show();
                $('#wcm-pos-products-list').hide();
            }
            
            $.ajax({
                url: wcm_pos_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wcm_pos_get_products',
                    nonce: wcm_pos_ajax.nonce,
                    search: search || '',
                    category: category || '',
                    limit: 20,
                    offset: replace ? 0 : WCMPOS.currentOffset
                },
                success: function(response) {
                    if (response.success) {
                        var products = response.data;
                        var $container = $('#wcm-pos-products-list');
                        
                        if (replace) {
                            $container.empty();
                            WCMPOS.currentOffset = 0;
                        }
                        
                        if (products.length > 0) {
                            WCMPOS.renderProducts(products, $container);
                            WCMPOS.currentOffset += products.length;
                            
                            // Show/hide load more button
                            if (products.length < 20) {
                                $('#wcm-pos-load-more').hide();
                            } else {
                                $('#wcm-pos-load-more').show();
                            }
                        } else if (replace) {
                            $container.html('<div class="wcm-pos-no-products"><p>' + wcm_pos_ajax.strings.no_products + '</p></div>');
                            $('#wcm-pos-load-more').hide();
                        }
                        
                        $('#wcm-pos-loading').hide();
                        $('#wcm-pos-products-list').show();
                    } else {
                        WCMPOS.showNotice(wcm_pos_ajax.strings.error_occurred, 'error');
                    }
                },
                error: function() {
                    WCMPOS.showNotice(wcm_pos_ajax.strings.error_occurred, 'error');
                },
                complete: function() {
                    WCMPOS.isLoading = false;
                }
            });
        },
        
        loadMoreProducts: function() {
            var search = $('#wcm-pos-search').val();
            var category = $('#wcm-pos-category-filter').val();
            
            WCMPOS.loadProducts(search, category, false);
        },
        
        renderProducts: function(products, $container) {
            products.forEach(function(product) {
                var imageHtml = product.image_url ? 
                    '<img src="' + product.image_url + '" alt="' + product.name + '">' :
                    '<div class="wcm-pos-no-image"><span class="dashicons dashicons-format-image"></span></div>';
                
                var saleBadge = product.sale_price ? '<div class="wcm-pos-sale-badge">Sale</div>' : '';
                
                var priceHtml = product.sale_price ?
                    '<span class="wcm-pos-regular-price">' + WCMPOS.formatPrice(product.regular_price) + '</span>' +
                    '<span class="wcm-pos-sale-price">' + WCMPOS.formatPrice(product.sale_price) + '</span>' :
                    '<span class="wcm-pos-current-price">' + WCMPOS.formatPrice(product.price) + '</span>';
                
                var stockHtml = product.stock_quantity !== null ?
                    '<span class="wcm-pos-stock-qty">Stock: ' + product.stock_quantity + '</span>' :
                    '<span class="wcm-pos-stock-status">' + product.stock_status + '</span>';
                
                var actionButton = product.type === 'variable' ?
                    '<button type="button" class="wcm-pos-select-variation-btn" data-product-id="' + product.id + '">' +
                    '<span class="dashicons dashicons-admin-settings"></span> Select Options</button>' :
                    '<button type="button" class="wcm-pos-add-to-cart-btn" data-product-id="' + product.id + '">' +
                    '<span class="dashicons dashicons-plus-alt"></span> Add to Cart</button>';
                
                var skuHtml = product.sku ? '<div class="wcm-pos-product-sku">SKU: ' + product.sku + '</div>' : '';
                
                var productHtml = 
                    '<div class="wcm-pos-product-card" data-product-id="' + product.id + '">' +
                        '<div class="wcm-pos-product-image">' +
                            imageHtml + saleBadge +
                        '</div>' +
                        '<div class="wcm-pos-product-info">' +
                            '<h3 class="wcm-pos-product-name">' + product.name + '</h3>' +
                            skuHtml +
                            '<div class="wcm-pos-product-price">' + priceHtml + '</div>' +
                            '<div class="wcm-pos-product-stock">' + stockHtml + '</div>' +
                            '<div class="wcm-pos-product-actions">' + actionButton + '</div>' +
                        '</div>' +
                    '</div>';
                
                $container.append(productHtml);
            });
        },
        
        addToCart: function() {
            var productId = $(this).data('product-id');
            var $btn = $(this);
            var originalText = $btn.html();

            console.log('Adding product to cart:', productId);

            $btn.prop('disabled', true).html('<span class="wcm-spinner"></span> Adding...');

            $.ajax({
                url: wcm_pos_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wcm_pos_add_to_cart',
                    nonce: wcm_pos_ajax.nonce,
                    product_id: productId,
                    quantity: 1
                },
                success: function(response) {
                    if (response.success) {
                        WCMPOS.updateCartDisplay(response.data);
                        WCMPOS.showNotice('Product added to cart!', 'success');
                    } else {
                        var errorMessage = response.data || wcm_pos_ajax.strings.error_occurred;
                        WCMPOS.showNotice(errorMessage, 'error');
                        console.error('Add to cart error:', response);
                    }
                },
                error: function(xhr, status, error) {
                    var errorMessage = 'Network error occurred. Please try again.';
                    if (xhr.responseJSON && xhr.responseJSON.data) {
                        errorMessage = xhr.responseJSON.data;
                    }
                    WCMPOS.showNotice(errorMessage, 'error');
                    console.error('AJAX error:', xhr, status, error);
                },
                complete: function() {
                    $btn.prop('disabled', false).html(originalText);
                }
            });
        },
        
        showVariationModal: function() {
            var productId = $(this).data('product-id');
            
            // Get product details with variations
            $.ajax({
                url: wcm_pos_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wcm_pos_get_product_details',
                    nonce: wcm_pos_ajax.nonce,
                    product_id: productId
                },
                success: function(response) {
                    if (response.success) {
                        WCMPOS.renderVariationModal(response.data);
                        $('#wcm-pos-variation-modal').addClass('wcm-modal-show').show();
                    } else {
                        WCMPOS.showNotice(response.data || wcm_pos_ajax.strings.error_occurred, 'error');
                    }
                },
                error: function() {
                    WCMPOS.showNotice(wcm_pos_ajax.strings.error_occurred, 'error');
                }
            });
        },
        
        renderVariationModal: function(product) {
            var modalBody = $('#wcm-pos-variation-modal-body');
            var html = '<div class="wcm-pos-variation-product">';
            
            html += '<h4>' + product.name + '</h4>';
            
            if (product.description) {
                html += '<p>' + product.description + '</p>';
            }
            
            html += '<div class="wcm-pos-variations">';
            
            product.variations.forEach(function(variation) {
                var attributesText = Object.values(variation.attributes).join(', ');
                var stockText = variation.stock_quantity !== null ? 
                    ' (Stock: ' + variation.stock_quantity + ')' : '';
                
                html += '<div class="wcm-pos-variation-option" data-variation-id="' + variation.id + '">';
                html += '<label>';
                html += '<input type="radio" name="selected_variation" value="' + variation.id + '">';
                html += '<span class="wcm-pos-variation-details">';
                html += '<strong>' + attributesText + '</strong>';
                html += '<span class="wcm-pos-variation-price">' + variation.formatted_price + '</span>';
                html += '<small>' + variation.sku + stockText + '</small>';
                html += '</span>';
                html += '</label>';
                html += '</div>';
            });
            
            html += '</div></div>';
            
            modalBody.html(html);
            
            // Store product ID for later use
            $('#wcm-pos-variation-add-to-cart').data('product-id', product.id);
        },
        
        addVariationToCart: function() {
            var productId = $(this).data('product-id');
            var selectedVariation = $('input[name="selected_variation"]:checked').val();
            
            if (!selectedVariation) {
                WCMPOS.showNotice('Please select a variation', 'error');
                return;
            }
            
            var $btn = $(this);
            var originalText = $btn.html();
            
            $btn.prop('disabled', true).html('<span class="wcm-spinner"></span> Adding...');
            
            $.ajax({
                url: wcm_pos_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wcm_pos_add_to_cart',
                    nonce: wcm_pos_ajax.nonce,
                    product_id: productId,
                    variation_id: selectedVariation,
                    quantity: 1
                },
                success: function(response) {
                    if (response.success) {
                        WCMPOS.updateCartDisplay(response.data);
                        WCMPOS.hideModal();
                        WCMPOS.showNotice('Product added to cart!', 'success');
                    } else {
                        WCMPOS.showNotice(response.data || wcm_pos_ajax.strings.error_occurred, 'error');
                    }
                },
                error: function() {
                    WCMPOS.showNotice(wcm_pos_ajax.strings.error_occurred, 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false).html(originalText);
                }
            });
        },

        decreaseQuantity: function() {
            var cartKey = $(this).data('cart-key');
            var $input = $('.wcm-pos-qty-input[data-cart-key="' + cartKey + '"]');
            var currentQty = parseInt($input.val());

            if (currentQty > 1) {
                WCMPOS.updateCartItemQuantity(cartKey, currentQty - 1);
            }
        },

        increaseQuantity: function() {
            var cartKey = $(this).data('cart-key');
            var $input = $('.wcm-pos-qty-input[data-cart-key="' + cartKey + '"]');
            var currentQty = parseInt($input.val());

            WCMPOS.updateCartItemQuantity(cartKey, currentQty + 1);
        },

        updateQuantity: function() {
            var cartKey = $(this).data('cart-key');
            var newQty = parseInt($(this).val());

            if (newQty > 0) {
                WCMPOS.updateCartItemQuantity(cartKey, newQty);
            }
        },

        updateCartItemQuantity: function(cartKey, quantity) {
            $.ajax({
                url: wcm_pos_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wcm_pos_update_cart',
                    nonce: wcm_pos_ajax.nonce,
                    cart_item_key: cartKey,
                    quantity: quantity
                },
                success: function(response) {
                    if (response.success) {
                        WCMPOS.updateCartDisplay(response.data);
                    } else {
                        WCMPOS.showNotice(response.data || wcm_pos_ajax.strings.error_occurred, 'error');
                    }
                },
                error: function() {
                    WCMPOS.showNotice(wcm_pos_ajax.strings.error_occurred, 'error');
                }
            });
        },

        removeFromCart: function() {
            var cartKey = $(this).data('cart-key');

            if (!confirm(wcm_pos_ajax.strings.remove_item)) {
                return;
            }

            $.ajax({
                url: wcm_pos_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wcm_pos_remove_from_cart',
                    nonce: wcm_pos_ajax.nonce,
                    cart_item_key: cartKey
                },
                success: function(response) {
                    if (response.success) {
                        WCMPOS.updateCartDisplay(response.data);
                        WCMPOS.showNotice('Item removed from cart', 'success');
                    } else {
                        WCMPOS.showNotice(response.data || wcm_pos_ajax.strings.error_occurred, 'error');
                    }
                },
                error: function() {
                    WCMPOS.showNotice(wcm_pos_ajax.strings.error_occurred, 'error');
                }
            });
        },

        clearCart: function() {
            if (!confirm(wcm_pos_ajax.strings.confirm_clear_cart)) {
                return;
            }

            $.ajax({
                url: wcm_pos_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wcm_pos_clear_cart',
                    nonce: wcm_pos_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        WCMPOS.updateCartDisplay({items: {}, totals: {subtotal: 0, tax_total: 0, total: 0, formatted_subtotal: '$0.00', formatted_tax_total: '$0.00', formatted_total: '$0.00'}});
                        WCMPOS.showNotice('Cart cleared', 'success');
                    } else {
                        WCMPOS.showNotice(response.data || wcm_pos_ajax.strings.error_occurred, 'error');
                    }
                },
                error: function() {
                    WCMPOS.showNotice(wcm_pos_ajax.strings.error_occurred, 'error');
                }
            });
        },

        updateCartDisplay: function(cartData) {
            var $cartItems = $('#wcm-pos-cart-items');
            var $checkoutBtn = $('#wcm-pos-checkout');

            console.log('Updating cart display with data:', cartData);

            if (!cartData) {
                // Get current cart data
                $.ajax({
                    url: wcm_pos_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'wcm_pos_get_cart',
                        nonce: wcm_pos_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            WCMPOS.updateCartDisplay(response.data);
                        }
                    }
                });
                return;
            }

            var items = cartData.items || {};
            var totals = cartData.totals || {};

            // Update cart items
            if (Object.keys(items).length === 0) {
                $cartItems.html(
                    '<div class="wcm-pos-empty-cart">' +
                        '<span class="dashicons dashicons-cart"></span>' +
                        '<p>Cart is empty</p>' +
                        '<small>Add products to get started</small>' +
                    '</div>'
                );
                $checkoutBtn.prop('disabled', true);
            } else {
                var itemsHtml = '';

                Object.keys(items).forEach(function(cartKey) {
                    var item = items[cartKey];
                    var skuHtml = item.sku ? '<small>SKU: ' + item.sku + '</small>' : '';

                    // Use formatted_price if available, otherwise format the price
                    var itemPrice = item.formatted_price || WCMPOS.formatPrice(item.price);
                    var itemTotal = item.formatted_price ?
                        WCMPOS.formatPrice(item.price * item.quantity) :
                        WCMPOS.formatPrice(item.price * item.quantity);

                    itemsHtml +=
                        '<div class="wcm-pos-cart-item" data-cart-key="' + cartKey + '">' +
                            '<div class="wcm-pos-cart-item-info">' +
                                '<h4>' + item.name + '</h4>' +
                                skuHtml +
                                '<div class="wcm-pos-cart-item-price">' + itemPrice + '</div>' +
                            '</div>' +
                            '<div class="wcm-pos-cart-item-controls">' +
                                '<div class="wcm-pos-quantity-controls">' +
                                    '<button type="button" class="wcm-pos-qty-decrease" data-cart-key="' + cartKey + '">-</button>' +
                                    '<input type="number" class="wcm-pos-qty-input" value="' + item.quantity + '" min="1" data-cart-key="' + cartKey + '">' +
                                    '<button type="button" class="wcm-pos-qty-increase" data-cart-key="' + cartKey + '">+</button>' +
                                '</div>' +
                                '<button type="button" class="wcm-pos-remove-item" data-cart-key="' + cartKey + '" title="Remove Item">' +
                                    '<span class="dashicons dashicons-no-alt"></span>' +
                                '</button>' +
                            '</div>' +
                            '<div class="wcm-pos-cart-item-total">' +
                                itemTotal +
                            '</div>' +
                        '</div>';
                });

                $cartItems.html(itemsHtml);
                $checkoutBtn.prop('disabled', false);
            }

            // Update totals - use text() to avoid HTML rendering and strip HTML if present
            var subtotalText = totals.formatted_subtotal || '$0.00';
            var taxText = totals.formatted_tax_total || '$0.00';
            var totalText = totals.formatted_total || '$0.00';

            // Strip HTML tags if present
            subtotalText = $('<div>').html(subtotalText).text();
            taxText = $('<div>').html(taxText).text();
            totalText = $('<div>').html(totalText).text();

            $('#wcm-pos-subtotal-amount').text(subtotalText);
            $('#wcm-pos-tax-amount').text(taxText);
            $('#wcm-pos-total-amount').text(totalText);

            // Show/hide tax row
            if (totals.tax_total > 0) {
                $('.wcm-pos-tax').show();
            } else {
                $('.wcm-pos-tax').hide();
            }
        },

        toggleCustomerForm: function() {
            var $form = $('#wcm-pos-customer-form');
            var $btn = $(this);

            if ($form.is(':visible')) {
                $form.slideUp();
                $btn.html('<span class="dashicons dashicons-admin-users"></span> Add Customer Info');
            } else {
                $form.slideDown();
                $btn.html('<span class="dashicons dashicons-admin-users"></span> Hide Customer Info');
            }
        },

        processCheckout: function() {
            var paymentMethod = $('input[name="payment_method"]:checked').val();

            if (!paymentMethod) {
                WCMPOS.showNotice('Please select a payment method', 'error');
                return;
            }

            var customerData = {};
            if ($('#wcm-pos-customer-form').is(':visible')) {
                customerData = {
                    first_name: $('#wcm-pos-customer-first-name').val(),
                    last_name: $('#wcm-pos-customer-last-name').val(),
                    email: $('#wcm-pos-customer-email').val(),
                    phone: $('#wcm-pos-customer-phone').val()
                };
            }

            var $btn = $(this);
            var originalText = $btn.html();

            $btn.prop('disabled', true).html('<span class="wcm-spinner"></span> ' + wcm_pos_ajax.strings.processing_order);
            $('.wcm-pos-cart-section').addClass('wcm-pos-processing');

            $.ajax({
                url: wcm_pos_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wcm_pos_create_order',
                    nonce: wcm_pos_ajax.nonce,
                    payment_method: paymentMethod,
                    customer_data: customerData
                },
                success: function(response) {
                    if (response.success) {
                        WCMPOS.showOrderReceipt(response.data);
                        WCMPOS.showNotice(wcm_pos_ajax.strings.order_completed, 'success');
                    } else {
                        WCMPOS.showNotice(response.data || wcm_pos_ajax.strings.error_occurred, 'error');
                    }
                },
                error: function() {
                    WCMPOS.showNotice(wcm_pos_ajax.strings.error_occurred, 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false).html(originalText);
                    $('.wcm-pos-cart-section').removeClass('wcm-pos-processing');
                }
            });
        },

        showOrderReceipt: function(orderData) {
            var receiptHtml = WCMPOS.generateReceiptHtml(orderData);
            $('#wcm-pos-receipt-content').html(receiptHtml);
            $('#wcm-pos-receipt-modal').addClass('wcm-modal-show').show();
        },

        generateReceiptHtml: function(orderData) {
            var html = '<div class="wcm-pos-receipt">';
            html += '<div class="wcm-pos-receipt-header">';
            html += '<h2>Order Receipt</h2>';
            html += '<div class="wcm-pos-receipt-order-info">';
            html += '<p><strong>Order #:</strong> ' + orderData.order_number + '</p>';
            html += '<p><strong>Date:</strong> ' + new Date().toLocaleDateString() + '</p>';
            html += '<p><strong>Status:</strong> ' + orderData.status.charAt(0).toUpperCase() + orderData.status.slice(1) + '</p>';
            html += '</div>';
            html += '</div>';

            html += '<div class="wcm-pos-receipt-total">';
            html += '<h3>Total: ' + orderData.formatted_total + '</h3>';
            html += '</div>';

            html += '<div class="wcm-pos-receipt-footer">';
            html += '<p>Thank you for your purchase!</p>';
            html += '</div>';
            html += '</div>';

            return html;
        },

        printReceipt: function() {
            var printContent = $('#wcm-pos-receipt-content').html();
            var printWindow = window.open('', '_blank');

            printWindow.document.write('<html><head><title>Receipt</title>');
            printWindow.document.write('<style>');
            printWindow.document.write('body { font-family: Arial, sans-serif; margin: 20px; }');
            printWindow.document.write('.wcm-pos-receipt { max-width: 300px; margin: 0 auto; }');
            printWindow.document.write('.wcm-pos-receipt-header { text-align: center; margin-bottom: 20px; border-bottom: 1px solid #ccc; padding-bottom: 10px; }');
            printWindow.document.write('.wcm-pos-receipt-total { text-align: center; margin: 20px 0; font-size: 18px; }');
            printWindow.document.write('.wcm-pos-receipt-footer { text-align: center; margin-top: 20px; border-top: 1px solid #ccc; padding-top: 10px; }');
            printWindow.document.write('</style>');
            printWindow.document.write('</head><body>');
            printWindow.document.write(printContent);
            printWindow.document.write('</body></html>');

            printWindow.document.close();
            printWindow.print();
            printWindow.close();
        },

        startNewSale: function() {
            WCMPOS.hideModal();

            // Clear customer form
            $('#wcm-pos-customer-form input').val('');
            $('#wcm-pos-customer-form').hide();
            $('#wcm-pos-toggle-customer').html('<span class="dashicons dashicons-admin-users"></span> Add Customer Info');

            // Reset payment method to cash
            $('input[name="payment_method"][value="cash"]').prop('checked', true);

            // Update cart display to show empty cart
            WCMPOS.updateCartDisplay();
        },

        hideModal: function() {
            $('.wcm-modal').removeClass('wcm-modal-show');
            setTimeout(function() {
                $('.wcm-modal').hide();
            }, 300);
        },

        formatPrice: function(price) {
            // Use WooCommerce currency formatting
            var symbol = wcm_pos_ajax.currency_symbol;
            var position = wcm_pos_ajax.currency_position;
            var decimals = wcm_pos_ajax.price_decimals;

            var formattedPrice = parseFloat(price).toFixed(decimals);

            switch (position) {
                case 'left':
                    return symbol + formattedPrice;
                case 'right':
                    return formattedPrice + symbol;
                case 'left_space':
                    return symbol + ' ' + formattedPrice;
                case 'right_space':
                    return formattedPrice + ' ' + symbol;
                default:
                    return symbol + formattedPrice;
            }
        },

        // Debug helper function
        debugCart: function() {
            $.ajax({
                url: wcm_pos_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wcm_pos_get_cart',
                    nonce: wcm_pos_ajax.nonce
                },
                success: function(response) {
                    console.log('Current cart data:', response);
                }
            });
        },

        showNotice: function(message, type) {
            type = type || 'info';

            // Create modern notification (reusing existing pattern)
            var iconClass = type === 'success' ? 'dashicons-yes-alt' :
                           type === 'error' ? 'dashicons-dismiss' : 'dashicons-info';

            var $notice = $('<div class="wcm-modern-notice wcm-notice-' + type + '">' +
                '<div class="wcm-notice-icon"><span class="dashicons ' + iconClass + '"></span></div>' +
                '<div class="wcm-notice-content">' + message + '</div>' +
                '<div class="wcm-notice-close"><span class="dashicons dashicons-no-alt"></span></div>' +
                '</div>');

            // Remove existing notices
            $('.wcm-modern-notice').remove();

            // Add new notice with animation
            $('body').append($notice);

            // Animate in
            setTimeout(function() {
                $notice.addClass('wcm-notice-show');
            }, 100);

            // Close button functionality
            $notice.find('.wcm-notice-close').on('click', function() {
                $notice.removeClass('wcm-notice-show');
                setTimeout(function() {
                    $notice.remove();
                }, 300);
            });

            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                if ($notice.length) {
                    $notice.removeClass('wcm-notice-show');
                    setTimeout(function() {
                        $notice.remove();
                    }, 300);
                }
            }, 5000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        WCMPOS.init();
    });

    // Expose to global scope for debugging
    window.WCMPOS = WCMPOS;

})(jQuery);
