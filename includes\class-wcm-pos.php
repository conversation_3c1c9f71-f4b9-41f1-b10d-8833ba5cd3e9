<?php
/**
 * POS functionality for Woo Cash Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WCM_POS {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Initialize cart using WordPress transients instead of sessions
        $this->init_cart();
    }

    /**
     * Initialize cart using WordPress transients
     */
    private function init_cart() {
        // Use user-specific transient for cart data
        $user_id = get_current_user_id();
        $cart_key = 'wcm_pos_cart_' . $user_id;

        // Get existing cart or create new one
        $cart = get_transient($cart_key);
        if (false === $cart) {
            $cart = array();
            set_transient($cart_key, $cart, 24 * HOUR_IN_SECONDS); // 24 hours expiry
        }
    }
    
    /**
     * Get products for POS display
     */
    public function get_pos_products($search = '', $category = '', $limit = 20, $offset = 0) {
        $args = array(
            'post_type' => 'product',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'offset' => $offset,
            'meta_query' => array(
                array(
                    'key' => '_stock_status',
                    'value' => 'instock',
                    'compare' => '='
                )
            )
        );
        
        // Add search functionality
        if (!empty($search)) {
            $args['s'] = sanitize_text_field($search);
        }
        
        // Add category filter
        if (!empty($category)) {
            $args['tax_query'] = array(
                array(
                    'taxonomy' => 'product_cat',
                    'field' => 'slug',
                    'terms' => sanitize_text_field($category)
                )
            );
        }
        
        $products = get_posts($args);
        $formatted_products = array();
        
        foreach ($products as $product_post) {
            $product = wc_get_product($product_post->ID);
            
            if (!$product || !$product->is_purchasable()) {
                continue;
            }
            
            $formatted_products[] = array(
                'id' => $product->get_id(),
                'name' => $product->get_name(),
                'price' => $product->get_price(),
                'regular_price' => $product->get_regular_price(),
                'sale_price' => $product->get_sale_price(),
                'formatted_price' => wc_price($product->get_price()),
                'sku' => $product->get_sku(),
                'stock_quantity' => $product->get_stock_quantity(),
                'stock_status' => $product->get_stock_status(),
                'image_url' => wp_get_attachment_image_url($product->get_image_id(), 'thumbnail'),
                'categories' => $this->get_product_categories($product->get_id()),
                'type' => $product->get_type(),
                'variations' => $product->is_type('variable') ? $this->get_product_variations($product->get_id()) : array()
            );
        }
        
        return $formatted_products;
    }
    
    /**
     * Get product categories
     */
    private function get_product_categories($product_id) {
        $terms = get_the_terms($product_id, 'product_cat');
        $categories = array();
        
        if ($terms && !is_wp_error($terms)) {
            foreach ($terms as $term) {
                $categories[] = array(
                    'id' => $term->term_id,
                    'name' => $term->name,
                    'slug' => $term->slug
                );
            }
        }
        
        return $categories;
    }
    
    /**
     * Get product variations for variable products
     */
    private function get_product_variations($product_id) {
        $product = wc_get_product($product_id);
        $variations = array();
        
        if ($product && $product->is_type('variable')) {
            $variation_ids = $product->get_children();
            
            foreach ($variation_ids as $variation_id) {
                $variation = wc_get_product($variation_id);
                
                if ($variation && $variation->is_purchasable()) {
                    $variations[] = array(
                        'id' => $variation->get_id(),
                        'attributes' => $variation->get_variation_attributes(),
                        'price' => $variation->get_price(),
                        'formatted_price' => wc_price($variation->get_price()),
                        'sku' => $variation->get_sku(),
                        'stock_quantity' => $variation->get_stock_quantity(),
                        'stock_status' => $variation->get_stock_status(),
                        'image_url' => wp_get_attachment_image_url($variation->get_image_id(), 'thumbnail')
                    );
                }
            }
        }
        
        return $variations;
    }
    
    /**
     * Get available payment methods
     */
    public function get_available_payment_methods() {
        $payment_gateways = WC()->payment_gateways->get_available_payment_gateways();
        $methods = array();
        
        // Always include cash payment
        $methods['cash'] = array(
            'id' => 'cash',
            'title' => __('Cash Payment', 'woo-cash-manager'),
            'description' => __('Accept cash payment and update cash balance', 'woo-cash-manager'),
            'icon' => 'dashicons-money-alt',
            'enabled' => true
        );
        
        // Add other available payment methods
        foreach ($payment_gateways as $gateway) {
            if ($gateway->enabled === 'yes') {
                $methods[$gateway->id] = array(
                    'id' => $gateway->id,
                    'title' => $gateway->get_title(),
                    'description' => $gateway->get_description(),
                    'icon' => $gateway->get_icon(),
                    'enabled' => true
                );
            }
        }
        
        return $methods;
    }
    
    /**
     * Add item to cart
     */
    public function add_to_cart($product_id, $quantity = 1, $variation_id = 0, $variation_data = array()) {
        // Check if WooCommerce is active
        if (!function_exists('wc_get_product')) {
            return false;
        }

        $product = wc_get_product($product_id);

        if (!$product || !$product->is_purchasable()) {
            return false;
        }

        // Check stock
        if (!$product->has_enough_stock($quantity)) {
            return false;
        }

        // Get current cart
        $cart = $this->get_cart();

        // Generate cart item key
        $cart_item_key = $this->generate_cart_item_key($product_id, $variation_id, $variation_data);

        // Check if item already exists in cart
        if (isset($cart[$cart_item_key])) {
            $cart[$cart_item_key]['quantity'] += $quantity;
        } else {
            $price = $variation_id ? wc_get_product($variation_id)->get_price() : $product->get_price();
            $cart[$cart_item_key] = array(
                'product_id' => $product_id,
                'variation_id' => $variation_id,
                'quantity' => $quantity,
                'variation_data' => $variation_data,
                'price' => $price,
                'formatted_price' => $this->format_price($price),
                'name' => $variation_id ? wc_get_product($variation_id)->get_name() : $product->get_name(),
                'sku' => $variation_id ? wc_get_product($variation_id)->get_sku() : $product->get_sku()
            );
        }

        // Save cart
        $this->save_cart($cart);

        return true;
    }
    
    /**
     * Get cart from transient
     */
    private function get_cart() {
        $user_id = get_current_user_id();
        $cart_key = 'wcm_pos_cart_' . $user_id;
        $cart = get_transient($cart_key);
        return $cart !== false ? $cart : array();
    }

    /**
     * Save cart to transient
     */
    private function save_cart($cart) {
        $user_id = get_current_user_id();
        $cart_key = 'wcm_pos_cart_' . $user_id;
        set_transient($cart_key, $cart, 24 * HOUR_IN_SECONDS);
    }

    /**
     * Remove item from cart
     */
    public function remove_from_cart($cart_item_key) {
        $cart = $this->get_cart();
        if (isset($cart[$cart_item_key])) {
            unset($cart[$cart_item_key]);
            $this->save_cart($cart);
            return true;
        }
        return false;
    }

    /**
     * Update cart item quantity
     */
    public function update_cart_quantity($cart_item_key, $quantity) {
        $cart = $this->get_cart();
        if (isset($cart[$cart_item_key])) {
            if ($quantity <= 0) {
                return $this->remove_from_cart($cart_item_key);
            }

            $cart[$cart_item_key]['quantity'] = $quantity;
            $this->save_cart($cart);
            return true;
        }
        return false;
    }

    /**
     * Clear cart
     */
    public function clear_cart() {
        $this->save_cart(array());
        return true;
    }

    /**
     * Get cart items
     */
    public function get_cart_items() {
        return $this->get_cart();
    }
    
    /**
     * Get cart totals
     */
    public function get_cart_totals() {
        $cart_items = $this->get_cart_items();
        $subtotal = 0;
        $total_items = 0;
        
        foreach ($cart_items as $item) {
            $line_total = $item['price'] * $item['quantity'];
            $subtotal += $line_total;
            $total_items += $item['quantity'];
        }
        
        // Calculate tax (using WooCommerce tax settings)
        $tax_total = 0;
        if (wc_tax_enabled()) {
            // Simplified tax calculation - in real implementation, this would be more complex
            $tax_rates = WC_Tax::get_rates();
            if (!empty($tax_rates)) {
                $tax_rate = reset($tax_rates);
                $tax_total = $subtotal * ($tax_rate['rate'] / 100);
            }
        }
        
        $total = $subtotal + $tax_total;
        
        return array(
            'subtotal' => $subtotal,
            'tax_total' => $tax_total,
            'total' => $total,
            'total_items' => $total_items,
            'formatted_subtotal' => $this->format_price($subtotal),
            'formatted_tax_total' => $this->format_price($tax_total),
            'formatted_total' => $this->format_price($total)
        );
    }

    /**
     * Format price without HTML tags
     */
    private function format_price($price) {
        if (!function_exists('get_woocommerce_currency_symbol')) {
            return '$' . number_format($price, 2);
        }

        $currency_symbol = get_woocommerce_currency_symbol();
        $currency_position = get_option('woocommerce_currency_pos', 'left');
        $decimals = wc_get_price_decimals();

        $formatted_price = number_format($price, $decimals);

        switch ($currency_position) {
            case 'left':
                return $currency_symbol . $formatted_price;
            case 'right':
                return $formatted_price . $currency_symbol;
            case 'left_space':
                return $currency_symbol . ' ' . $formatted_price;
            case 'right_space':
                return $formatted_price . ' ' . $currency_symbol;
            default:
                return $currency_symbol . $formatted_price;
        }
    }

    /**
     * Generate cart item key
     */
    private function generate_cart_item_key($product_id, $variation_id = 0, $variation_data = array()) {
        $key_parts = array($product_id);

        if ($variation_id) {
            $key_parts[] = $variation_id;
        }

        if (!empty($variation_data)) {
            ksort($variation_data);
            $key_parts[] = md5(serialize($variation_data));
        }

        return implode('_', $key_parts);
    }

    /**
     * Create WooCommerce order from POS cart
     */
    public function create_order($payment_method = 'cash', $customer_data = array()) {
        $cart_items = $this->get_cart_items();

        if (empty($cart_items)) {
            return false;
        }

        try {
            // Create new order
            $order = wc_create_order();

            if (is_wp_error($order)) {
                return false;
            }

            // Add customer data if provided
            if (!empty($customer_data)) {
                $order->set_billing_first_name($customer_data['first_name'] ?? '');
                $order->set_billing_last_name($customer_data['last_name'] ?? '');
                $order->set_billing_email($customer_data['email'] ?? '');
                $order->set_billing_phone($customer_data['phone'] ?? '');
            }

            // Add items to order
            foreach ($cart_items as $item) {
                $product = wc_get_product($item['variation_id'] ?: $item['product_id']);

                if ($product) {
                    $order->add_product($product, $item['quantity']);
                }
            }

            // Calculate totals
            $order->calculate_totals();

            // Set payment method
            $order->set_payment_method($payment_method);

            // Set order status based on payment method
            if ($payment_method === 'cash') {
                $order->set_status('completed');

                // Add cash payment to cash management system
                $this->add_cash_transaction($order->get_total(), $order->get_id());
            } else {
                $order->set_status('processing');
            }

            // Add order note
            $order->add_order_note(__('Order created via POS system.', 'woo-cash-manager'));

            // Save order
            $order->save();

            // Clear cart after successful order creation
            $this->clear_cart();

            return $order->get_id();

        } catch (Exception $e) {
            error_log('POS Order Creation Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Add cash transaction to cash management system
     */
    private function add_cash_transaction($amount, $order_id) {
        // This integrates with the existing cash management system
        // Cash payments are automatically tracked as income through WooCommerce orders
        // The existing system already pulls income from completed orders

        // We don't need to add a separate expense record since the income
        // is already calculated from WooCommerce orders in the dashboard

        // Optional: Add a note to the order for tracking
        $order = wc_get_order($order_id);
        if ($order) {
            $order->add_order_note(
                sprintf(__('Cash payment of %s received via POS system.', 'woo-cash-manager'), wc_price($amount))
            );
        }

        // Fire action hook for extensibility
        do_action('wcm_pos_cash_payment_received', $amount, $order_id);
    }

    /**
     * Get product categories for filter
     */
    public function get_all_product_categories() {
        $categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => true,
            'orderby' => 'name',
            'order' => 'ASC'
        ));

        $formatted_categories = array();

        if (!is_wp_error($categories)) {
            foreach ($categories as $category) {
                $formatted_categories[] = array(
                    'id' => $category->term_id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'count' => $category->count
                );
            }
        }

        return $formatted_categories;
    }

    /**
     * Search products by term
     */
    public function search_products($term, $limit = 10) {
        $args = array(
            'post_type' => 'product',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            's' => sanitize_text_field($term),
            'meta_query' => array(
                array(
                    'key' => '_stock_status',
                    'value' => 'instock',
                    'compare' => '='
                )
            )
        );

        $products = get_posts($args);
        $results = array();

        foreach ($products as $product_post) {
            $product = wc_get_product($product_post->ID);

            if ($product && $product->is_purchasable()) {
                $results[] = array(
                    'id' => $product->get_id(),
                    'name' => $product->get_name(),
                    'sku' => $product->get_sku(),
                    'price' => $product->get_price(),
                    'formatted_price' => wc_price($product->get_price()),
                    'image_url' => wp_get_attachment_image_url($product->get_image_id(), 'thumbnail')
                );
            }
        }

        return $results;
    }
}
